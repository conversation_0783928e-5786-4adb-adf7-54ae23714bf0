# Compilation Test Guide

## Before Compiling

1. **Install Required Libraries**
   - Follow the instructions in `library_requirements.txt`
   - Ensure all libraries are installed correctly

2. **ESP32 Board Setup**
   - Install ESP32 board package in Arduino IDE
   - Select correct board: "ESP32 Dev Module" or "DOIT ESP32 DEVKIT V1"
   - Select correct COM port

## Compilation Steps

1. Open `quadcopter_flight_controller.ino` in Arduino IDE
2. Verify/Compile (Ctrl+R or checkmark button)
3. Check for any errors in the output window

## Common Issues and Solutions

### Library Not Found Errors
```
fatal error: MPU6050_tockn.h: No such file or directory
```
**Solution**: Install the missing library via Library Manager

### ESP32 Board Not Found
```
Error: Board esp32:esp32:esp32 not found
```
**Solution**: Install ESP32 board package via Board Manager

### Compilation Errors After Library Installation
```
'class MPU6050' has no member named 'begin'
```
**Solution**: You may have installed the wrong MPU6050 library. Use "MPU6050_tockn" by tockn

### Memory Issues
```
Sketch too big; see http://www.arduino.cc/en/Guide/Troubleshooting#size
```
**Solution**: 
- Select a board with more memory (ESP32 has plenty)
- Reduce debug output if needed

## Expected Compilation Output

When successful, you should see:
```
Sketch uses XXXXX bytes (XX%) of program storage space.
Global variables use XXXXX bytes (XX%) of dynamic memory.
```

## Testing Without Hardware

The code will compile successfully even without hardware connected. However, for actual testing:

1. **Remove propellers** before any testing
2. Connect only the ESP32 initially
3. Upload code and check serial monitor
4. Add sensors one by one and test each

## Serial Monitor Output

After successful upload, you should see:
```
QuadFly Flight Controller Starting...
Initializing sensors...
MPU6050 initialized
HMC5883L initialized
MS5611 initialized
GPS Serial initialized
All sensors initialized successfully!
...
```

## Next Steps After Successful Compilation

1. **Hardware Connection**: Wire components according to README.md
2. **Initial Testing**: Upload code and check serial output
3. **Sensor Verification**: Verify each sensor provides data
4. **RC Testing**: Test RC input recognition
5. **Motor Testing**: Test motor outputs (without propellers!)
6. **Calibration**: Run sensor calibration procedures
7. **Flight Testing**: Only after thorough ground testing

## Troubleshooting Resources

- Arduino IDE error messages
- Serial monitor output
- ESP32 documentation
- Library documentation on GitHub
- QuadFly project README.md

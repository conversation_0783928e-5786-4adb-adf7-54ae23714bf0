/*
 * Quadcopter Flight Controller
 * ESP32-based flight controller with GY-86 10DOF, GPS, RC receiver, and TFT display
 * 
 * Hardware:
 * - ESP32 DevKit
 * - GY-86 10DOF (MPU6050 + HMC5883L + MS5611)
 * - NEO-6M GPS module
 * - FS-TH9X RC receiver
 * - 1.8" TFT ST7735 display
 * - 4x ESCs for motor control
 * 
 * Author: QuadFly Project
 * Version: 1.2 - Enhanced calibration and RC validation
 */

// ===== LIBRARY INCLUDES =====
#include <Wire.h>
#include <SPI.h>
#include <MPU6050_tockn.h>
#include <HMC5883L.h>
#include <MS5611.h>
#include <HardwareSerial.h>
#include <TinyGPS++.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>
#include <ESP32Servo.h>
#include <EEPROM.h>
#include <Preferences.h>
#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <SD.h>
#include <FS.h>
#include <SPIFFS.h>

// ===== PIN DEFINITIONS =====
// I2C pins (default for ESP32)
#define SDA_PIN 21
#define SCL_PIN 22

// GPS pins
#define GPS_RX_PIN 16
#define GPS_TX_PIN 17

// RC receiver pins (PWM input)
#define RC_CH1_PIN 14  // Throttle
#define RC_CH2_PIN 13  // Roll
#define RC_CH3_PIN 12  // Pitch
#define RC_CH4_PIN 27  // Yaw
#define RC_CH5_PIN 26  // Flight mode
#define RC_CH6_PIN 25  // Aux 1
#define RC_CH7_PIN 33  // Aux 2
#define RC_CH8_PIN 34  // Aux 3

// Motor ESC pins (PWM output)
#define MOTOR1_PIN 32   // Front Right
#define MOTOR2_PIN 15   // Front Left
#define MOTOR3_PIN 0   // Rear Left
#define MOTOR4_PIN 19  // Rear Right

// TFT display pins
#define TFT_CS_PIN 5
#define TFT_RST_PIN 4
#define TFT_DC_PIN 2
#define TFT_MOSI_PIN 23
#define TFT_SCLK_PIN 18

// Status LED
#define LED_PIN 2

// Battery voltage monitoring
#define BATTERY_PIN 35

// ===== EEPROM ADDRESSES =====
#define EEPROM_SIZE 512
#define EEPROM_RC_CAL_ADDR 0
#define EEPROM_IMU_CAL_ADDR 50
#define EEPROM_MAG_CAL_ADDR 100
#define EEPROM_BARO_CAL_ADDR 150
#define EEPROM_MAGIC_NUMBER 0xABCD  // To verify valid calibration data

// ===== CONSTANTS =====
#define LOOP_FREQUENCY 250  // Hz
#define LOOP_TIME 1000000 / LOOP_FREQUENCY  // microseconds

// RC channel limits
#define RC_MIN 1000
#define RC_MID 1500
#define RC_MAX 2000
#define RC_DEADBAND 50

// Motor limits
#define MOTOR_MIN 1000
#define MOTOR_MAX 2000
#define MOTOR_IDLE 1100

// PID constants
#define PID_ROLL_KP 1.5
#define PID_ROLL_KI 0.05
#define PID_ROLL_KD 0.8

#define PID_PITCH_KP 1.5
#define PID_PITCH_KI 0.05
#define PID_PITCH_KD 0.8

#define PID_YAW_KP 2.0
#define PID_YAW_KI 0.1
#define PID_YAW_KD 0.5

#define PID_ALT_KP 3.0
#define PID_ALT_KI 0.1
#define PID_ALT_KD 1.0

// Enhanced flight modes
enum FlightMode {
  MANUAL = 0,           // No stabilization - direct motor control
  STABILIZE = 1,        // Attitude stabilization
  ALTITUDE_HOLD = 2,    // Stabilization + altitude hold
  GPS_HOLD = 3,         // Position hold using GPS
  RETURN_TO_HOME = 4,   // Autonomous return to launch
  ACRO = 5,             // Rate mode for aerobatics
  CIRCLE = 6,           // Circle around point
  AUTO_LAND = 7,        // Automatic landing
  FAILSAFE = 8          // Emergency mode
};

// PID tuning modes
enum PIDTuningMode {
  TUNING_OFF = 0,
  TUNING_ROLL = 1,
  TUNING_PITCH = 2,
  TUNING_YAW = 3,
  TUNING_ALTITUDE = 4
};

// ===== GLOBAL VARIABLES =====
// Sensor objects
MPU6050 mpu(Wire);
HMC5883L compass;
MS5611 barometer;  // MS5611 barometric pressure sensor
TinyGPSPlus gps;
HardwareSerial gpsSerial(2);  // Use UART2 for GPS
Adafruit_ST7735 tft = Adafruit_ST7735(TFT_CS_PIN, TFT_DC_PIN, TFT_RST_PIN);

// Storage for calibration data
Preferences preferences;

// Servo objects for ESCs
Servo motor1, motor2, motor3, motor4;

// Sensor data
struct SensorData {
  // IMU data
  float accelX, accelY, accelZ;
  float gyroX, gyroY, gyroZ;
  float roll, pitch, yaw;
  
  // Magnetometer
  float magX, magY, magZ;
  float heading;
  
  // Barometer
  float pressure;
  float altitude;
  float temperature;
  
  // GPS
  double latitude, longitude;
  float gpsAltitude;
  int satellites;
  bool gpsValid;
  
  // Battery
  float batteryVoltage;
} sensors;

// RC input data
struct RCData {
  int throttle, roll, pitch, yaw;
  int aux1, aux2, aux3, aux4;
  bool signalLost;
  unsigned long lastUpdate;

  // RC Signal Quality
  int signalQuality;        // 0-100%
  int failedReads;          // Count of failed reads
  int totalReads;           // Total read attempts
  unsigned long lastGoodSignal; // Last time we had good signal
} rcInput;

// Enhanced PID controllers
struct PIDController {
  float kp, ki, kd;
  float setpoint, input, output;
  float integral, lastError;
  float outputMin, outputMax;

  // Advanced PID features
  float integralMax;        // Integral windup limit
  float derivativeFilter;   // Derivative filtering coefficient
  float lastInput;          // For derivative on measurement
  bool derivativeOnMeasurement; // Use derivative on measurement instead of error

  // Adaptive features
  float adaptiveKp, adaptiveKi, adaptiveKd; // Adaptive gains
  bool adaptiveMode;        // Enable adaptive PID

  // Rate vs Angle mode
  bool rateMode;            // True for rate mode, false for angle mode
  float rateLimit;          // Maximum rate for angle mode

  // Performance tracking
  float errorSum;           // For RMS error calculation
  int sampleCount;          // Sample counter
  unsigned long lastUpdate; // Last update time
} pidRoll, pidPitch, pidYaw, pidAlt;

// Motor outputs
int motor1Speed, motor2Speed, motor3Speed, motor4Speed;

// Flight control variables
FlightMode currentMode = STABILIZE;
bool armed = false;
bool calibrationMode = false;
unsigned long lastLoopTime = 0;
unsigned long displayUpdateTime = 0;

// Enhanced display variables
enum DisplayMode {
  DISPLAY_BASIC = 0,
  DISPLAY_ARTIFICIAL_HORIZON = 1,
  DISPLAY_GPS_MAP = 2,
  DISPLAY_PERFORMANCE = 3,
  DISPLAY_DIAGNOSTICS = 4
};

DisplayMode currentDisplayMode = DISPLAY_BASIC;
bool displayNeedsUpdate = true;
unsigned long lastDisplayModeChange = 0;

// Data logging system
struct DataLogger {
  bool enabled;
  bool sdCardAvailable;
  File logFile;
  unsigned long lastLogTime;
  int logInterval;              // Logging interval in ms
  unsigned long sessionStartTime;
  int flightNumber;
  char currentLogFileName[32];
} dataLogger;

// WiFi telemetry system
struct TelemetrySystem {
  bool wifiEnabled;
  bool clientConnected;
  WebServer* webServer;
  unsigned long lastTelemetryTime;
  int telemetryInterval;        // Telemetry interval in ms
  char ssid[32];
  char password[32];
  IPAddress localIP;
} telemetry;

// GPS tracking and mapping
struct GPSTracker {
  float homeLatitude;
  float homeLongitude;
  float homeAltitude;
  bool homeSet;
  float maxDistance;
  float currentDistance;
  float bearing;
  int satelliteCount;
  bool fix3D;
  float hdop;                   // Horizontal dilution of precision
} gpsTracker;

// PID tuning variables
PIDTuningMode tuningMode = TUNING_OFF;
float tuningStep = 0.1;           // PID adjustment step size
bool tuningActive = false;        // PID tuning in progress
unsigned long tuningStartTime = 0; // Tuning session start time

// Flight performance tracking
struct FlightPerformance {
  float rollRMS;                  // RMS error for roll
  float pitchRMS;                 // RMS error for pitch
  float yawRMS;                   // RMS error for yaw
  float altitudeRMS;              // RMS error for altitude
  float vibrationLevel;           // Vibration level
  float loopTime;                 // Average loop time
  int loopTimeViolations;         // Count of loop time violations
  unsigned long flightTime;      // Total flight time
  bool performanceLogging;        // Enable performance logging
} performance;

// Sensor status flags
bool mpu6050_available = false;
bool hmc5883l_available = false;
bool ms5611_available = false;

// Enhanced calibration data structures
struct IMUCalibration {
  float accelOffsetX, accelOffsetY, accelOffsetZ;
  float accelScaleX, accelScaleY, accelScaleZ;
  float gyroOffsetX, gyroOffsetY, gyroOffsetZ;
  float gyroDriftX, gyroDriftY, gyroDriftZ;  // Temperature compensation
  float temperature;  // Reference temperature
  bool valid;
} imuCal;

struct MagCalibration {
  float offsetX, offsetY, offsetZ;  // Hard iron correction
  float scaleX, scaleY, scaleZ;     // Soft iron correction
  float declination;                // Magnetic declination
  bool valid;
} magCal;

struct BaroCalibration {
  float pressureOffset;
  float temperatureOffset;
  float seaLevelPressure;
  bool valid;
} baroCal;

// Legacy variables for compatibility
float accelOffsetX = 0, accelOffsetY = 0, accelOffsetZ = 0;
float gyroOffsetX = 0, gyroOffsetY = 0, gyroOffsetZ = 0;
float magOffsetX = 0, magOffsetY = 0, magOffsetZ = 0;

// Barometer baseline
float baselinePressure = 101325.0;  // Sea level pressure in Pa

// Fail-safe variables
unsigned long rcLostTime = 0;
bool emergencyLanding = false;
float homeLatitude = 0, homeLongitude = 0;
float homeAltitude = 0;

// ===== SETUP FUNCTION =====
void setup() {
  Serial.begin(115200);
  Serial.println("QuadFly Flight Controller Starting...");
  
  // Initialize pins
  pinMode(LED_PIN, OUTPUT);
  pinMode(BATTERY_PIN, INPUT);
  
  // Initialize I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000);  // 400kHz I2C speed

  // Initialize display
  initDisplay();

  // Initialize enhanced UI systems
  initDataLogger();
  initWiFiTelemetry();
  initGPSTracker();

  // Initialize sensors
  if (!initSensors()) {
    Serial.println("Sensor initialization failed!");
    displayError("SENSOR INIT FAILED");
    while(1) {
      digitalWrite(LED_PIN, !digitalRead(LED_PIN));
      delay(200);
    }
  }
  
  // Initialize RC inputs
  initRCInputs();
  
  // Initialize motors
  initMotors();
  
  // Initialize PID controllers
  initPIDControllers();

  // Load saved PID settings
  loadPIDSettings();

  // Load saved calibration data
  loadSensorCalibration();

  // Calibrate sensors (will use saved data if available)
  calibrateSensors();
  
  Serial.println("Initialization complete!");
  displayMessage("READY TO FLY");
  
  // Set home position when GPS gets fix
  setHomePosition();
}

// ===== MAIN LOOP =====
void loop() {
  unsigned long currentTime = micros();
  
  // Maintain loop frequency
  if (currentTime - lastLoopTime >= LOOP_TIME) {
    lastLoopTime = currentTime;
    
    // Read all sensors
    readSensors();
    
    // Read RC inputs
    readRCInputs();

    // Check for PID tuning mode trigger (AUX1 + AUX3 when disarmed)
    if (!armed && rcInput.aux1 > 1700 && rcInput.aux3 > 1700 && !tuningActive) {
      // Determine tuning mode based on AUX2 position
      if (rcInput.aux2 < 1300) {
        startPIDTuning(TUNING_ROLL);
      } else if (rcInput.aux2 < 1500) {
        startPIDTuning(TUNING_PITCH);
      } else if (rcInput.aux2 < 1700) {
        startPIDTuning(TUNING_YAW);
      } else {
        startPIDTuning(TUNING_ALTITUDE);
      }
    }

    // Update PID tuning if active
    updatePIDTuning();

    // Check for full sensor recalibration trigger (AUX1 + AUX2 both high when disarmed)
    if (!armed && rcInput.aux1 > 1700 && rcInput.aux2 > 1700 && !tuningActive) {
      forceFullCalibration();
    }

    // Check fail-safe conditions
    checkFailSafe();
    
    // Update flight mode
    updateFlightMode();
    
    // Calculate attitude (sensor fusion)
    calculateAttitude();
    
    // Run PID controllers
    runPIDControllers();
    
    // Calculate motor outputs
    calculateMotorOutputs();
    
    // Update motor speeds
    updateMotors();

    // Update performance metrics
    updatePerformanceMetrics();

    // Detect vibration
    detectVibration();

    // Update GPS tracking
    updateGPSTracker();

    // Update telemetry system
    updateTelemetry();

    // Log flight data
    logFlightData();

    // Update display (at lower frequency)
    if (currentTime - displayUpdateTime > 100000) {  // 10Hz display update
      updateDisplay();
      displayUpdateTime = currentTime;
    }

    // Debug output (uncomment for RC/flight mode debugging)
    static unsigned long lastDebugTime = 0;
    if (currentTime - lastDebugTime > 1000000) {  // 1Hz debug output
      printRCDebug();  // Shows RC values and flight mode
      // printYawDebug();  // Uncomment for yaw debugging
      lastDebugTime = currentTime;
    }

    // Status LED
    digitalWrite(LED_PIN, armed);
  }
  
  // Process GPS data
  while (gpsSerial.available() > 0) {
    if (gps.encode(gpsSerial.read())) {
      updateGPSData();
    }
  }
}

// ===== INITIALIZATION FUNCTIONS =====

bool initSensors() {
  Serial.println("Initializing sensors...");

  // Initialize I2C with proper settings
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000);  // 400kHz I2C speed

  // Scan I2C bus first
  scanI2CDevices();

  // Initialize MPU6050
  Serial.println("Initializing MPU6050...");
  mpu.begin();
  mpu.calcGyroOffsets(true);  // Calculate gyro offsets

  // CRITICAL: Enable I2C bypass mode for HMC5883L access
  Serial.println("Enabling MPU6050 I2C bypass mode for HMC5883L...");

  // Access MPU6050 registers directly to enable bypass
  Wire.beginTransmission(0x68);  // MPU6050 address
  Wire.write(0x37);              // INT_PIN_CFG register
  Wire.write(0x02);              // Enable I2C bypass (bit 1)
  Wire.endTransmission();

  delay(100);  // Give time for bypass to activate

  mpu6050_available = true;
  Serial.println("MPU6050 initialized with I2C bypass enabled");

  // Initialize HMC5883L (now accessible via MPU6050 bypass)
  Serial.println("Initializing HMC5883L via MPU6050 bypass...");
  hmc5883l_available = false;

  // Wait for bypass mode to stabilize
  delay(200);

  // Try to initialize HMC5883L at standard address 0x1E
  for (int attempt = 0; attempt < 3; attempt++) {
    Serial.print("HMC5883L initialization attempt ");
    Serial.print(attempt + 1);
    Serial.println("/3");

    if (compass.begin()) {
      Serial.println("HMC5883L initialized successfully!");

      // Configure the sensor
      compass.setRange(HMC5883L_RANGE_1_3GA);
      compass.setMeasurementMode(HMC5883L_CONTINOUS);
      compass.setDataRate(HMC5883L_DATARATE_15HZ);
      compass.setSamples(HMC5883L_SAMPLES_8);

      // Test reading from the sensor
      delay(100);
      Vector mag = compass.readNormalize();
      if (mag.XAxis != 0 || mag.YAxis != 0 || mag.ZAxis != 0) {
        Serial.print("Test reading - X:");
        Serial.print(mag.XAxis);
        Serial.print(" Y:");
        Serial.print(mag.YAxis);
        Serial.print(" Z:");
        Serial.println(mag.ZAxis);
        hmc5883l_available = true;
        break;
      } else {
        Serial.println("HMC5883L initialized but test read failed");
      }
    } else {
      Serial.println("HMC5883L initialization failed, retrying...");
    }
    delay(500);
  }

  if (!hmc5883l_available) {
    Serial.println("HMC5883L initialization failed!");
    Serial.println("Continuing without magnetometer...");
    Serial.println();
    Serial.println("=== HMC5883L TROUBLESHOOTING (GY-86) ===");
    Serial.println("1. MPU6050 bypass mode enabled: Check if MPU6050 is working");
    Serial.println("2. HMC5883L is connected via MPU6050 auxiliary I2C bus");
    Serial.println("3. Check power supply: Module needs exactly 3.3V");
    Serial.println("4. Check I2C connections to GY-86 module:");
    Serial.println("   - SDA (pin 21) connected to GY-86 SDA");
    Serial.println("   - SCL (pin 22) connected to GY-86 SCL");
    Serial.println("5. Verify GY-86 module has working HMC5883L chip");
    Serial.println("6. Some clone modules have non-functional magnetometers");
    Serial.println("==========================================");
    Serial.println();
  }

  // Reset I2C speed back to normal after HMC5883L init
  Wire.setClock(400000);

  // Initialize MS5611
  Serial.println("Initializing MS5611...");
  if (!barometer.begin()) {
    Serial.println("MS5611 initialization failed!");
    Serial.println("Continuing without barometer...");
    ms5611_available = false;
  } else {
    Serial.println("MS5611 initialized");
    ms5611_available = true;
  }

  // Initialize GPS Serial
  gpsSerial.begin(38400, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);
  Serial.println("GPS Serial initialized");

  Serial.println("Sensor initialization complete!");
  return true;
}

void testI2CBus() {
  Serial.println("I2C Bus Diagnostic Test:");
  Serial.print("SDA Pin: "); Serial.println(SDA_PIN);
  Serial.print("SCL Pin: "); Serial.println(SCL_PIN);
  Serial.print("I2C Speed: "); Serial.println("100kHz");

  // Test I2C bus by scanning for any devices
  int deviceCount = 0;
  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    if (Wire.endTransmission() == 0) {
      deviceCount++;
      Serial.print("Device found at 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
    }
  }

  if (deviceCount == 0) {
    Serial.println("WARNING: No I2C devices found!");
    Serial.println("Check I2C wiring and pull-up resistors");
  } else {
    Serial.print("Found "); Serial.print(deviceCount); Serial.println(" I2C devices");
  }
  Serial.println();
}

// Also add this improved I2C scanner function
void scanI2CDevices() {
  Serial.println("Scanning I2C bus...");
  byte error, address;
  int nDevices = 0;

  // Reset I2C bus
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000);  // Use slower speed for scanning
  delay(100);

  for(address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    error = Wire.endTransmission();

    if (error == 0) {
      Serial.print("I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);

      // Identify known devices
      switch(address) {
        case 0x68: Serial.println(" (MPU6050)"); break;
        case 0x1E: Serial.println(" (HMC5883L)"); break;
        case 0x0D: Serial.println(" (HMC5883L - alternate)"); break;
        case 0x3C: Serial.println(" (HMC5883L - alternate)"); break;
        case 0x3D: Serial.println(" (HMC5883L - alternate)"); break;
        case 0x77: Serial.println(" (MS5611)"); break;
        default: Serial.println(" (Unknown)"); break;
      }
      nDevices++;
    }
    else if (error == 4) {
      Serial.print("Unknown error at address 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
    }
  }

  if (nDevices == 0) {
    Serial.println("No I2C devices found!");
  } else {
    Serial.print("Found ");
    Serial.print(nDevices);
    Serial.println(" I2C devices");
  }
  
  // Reset to normal I2C speed
  Wire.setClock(400000);
  Serial.println();
}

void initDisplay() {
  tft.initR(INITR_BLACKTAB);
  tft.setRotation(1);
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);
  tft.println("QuadFly FC v1.0");
  tft.println("Initializing...");
}

void initRCInputs() {
  // Configure RC input pins
  pinMode(RC_CH1_PIN, INPUT);
  pinMode(RC_CH2_PIN, INPUT);
  pinMode(RC_CH3_PIN, INPUT);
  pinMode(RC_CH4_PIN, INPUT);
  pinMode(RC_CH5_PIN, INPUT);
  pinMode(RC_CH6_PIN, INPUT);
  pinMode(RC_CH7_PIN, INPUT);
  pinMode(RC_CH8_PIN, INPUT);

  // Initialize RC data
  rcInput.throttle = RC_MIN;
  rcInput.roll = RC_MID;
  rcInput.pitch = RC_MID;
  rcInput.yaw = RC_MID;
  rcInput.signalLost = true;
  rcInput.lastUpdate = millis();

  // Initialize RC signal quality
  rcInput.signalQuality = 0;
  rcInput.failedReads = 0;
  rcInput.totalReads = 0;
  rcInput.lastGoodSignal = millis();
}

void initMotors() {
  // Attach ESCs to servo pins
  motor1.attach(MOTOR1_PIN, MOTOR_MIN, MOTOR_MAX);
  motor2.attach(MOTOR2_PIN, MOTOR_MIN, MOTOR_MAX);
  motor3.attach(MOTOR3_PIN, MOTOR_MIN, MOTOR_MAX);
  motor4.attach(MOTOR4_PIN, MOTOR_MIN, MOTOR_MAX);

  // Initialize motors to minimum throttle
  motor1.writeMicroseconds(MOTOR_MIN);
  motor2.writeMicroseconds(MOTOR_MIN);
  motor3.writeMicroseconds(MOTOR_MIN);
  motor4.writeMicroseconds(MOTOR_MIN);

  delay(2000);  // ESC initialization delay
}

void initPIDControllers() {
  // Initialize Roll PID
  initSinglePID(&pidRoll, PID_ROLL_KP, PID_ROLL_KI, PID_ROLL_KD, -400, 400, false, 30.0);

  // Initialize Pitch PID
  initSinglePID(&pidPitch, PID_PITCH_KP, PID_PITCH_KI, PID_PITCH_KD, -400, 400, false, 30.0);

  // Initialize Yaw PID (rate mode by default)
  initSinglePID(&pidYaw, PID_YAW_KP, PID_YAW_KI, PID_YAW_KD, -400, 400, true, 180.0);

  // Initialize Altitude PID
  initSinglePID(&pidAlt, PID_ALT_KP, PID_ALT_KI, PID_ALT_KD, -500, 500, false, 5.0);

  // Initialize performance tracking
  performance.performanceLogging = true;
  performance.rollRMS = 0;
  performance.pitchRMS = 0;
  performance.yawRMS = 0;
  performance.altitudeRMS = 0;
  performance.vibrationLevel = 0;
  performance.loopTime = 0;
  performance.loopTimeViolations = 0;
  performance.flightTime = 0;
}

void initSinglePID(PIDController* pid, float kp, float ki, float kd, float minOut, float maxOut, bool rateMode, float rateLimit) {
  // Basic PID parameters
  pid->kp = kp;
  pid->ki = ki;
  pid->kd = kd;
  pid->outputMin = minOut;
  pid->outputMax = maxOut;

  // Initialize advanced features
  pid->integralMax = 100.0;           // Integral windup limit
  pid->derivativeFilter = 0.1;        // 10Hz derivative filter
  pid->derivativeOnMeasurement = true; // Use derivative on measurement
  pid->adaptiveMode = false;          // Disable adaptive mode initially
  pid->rateMode = rateMode;           // Set rate/angle mode
  pid->rateLimit = rateLimit;         // Set rate limit for angle mode

  // Initialize runtime variables
  pid->setpoint = 0;
  pid->input = 0;
  pid->output = 0;
  pid->integral = 0;
  pid->lastError = 0;
  pid->lastInput = 0;
  pid->errorSum = 0;
  pid->sampleCount = 0;
  pid->lastUpdate = 0;

  // Set adaptive gains same as base gains initially
  pid->adaptiveKp = kp;
  pid->adaptiveKi = ki;
  pid->adaptiveKd = kd;
}

// ===== SENSOR READING FUNCTIONS =====

void readSensors() {
  static unsigned long lastSensorRead = 0;
  static int sensorErrors = 0;
  static int consecutiveHMCErrors = 0;

  // Limit sensor reading frequency to reduce I2C errors
  if (millis() - lastSensorRead < 10) return;  // Max 100Hz sensor reading
  lastSensorRead = millis();

  // Read MPU6050 with error handling
  if (mpu6050_available) {
    mpu.update();

    sensors.accelX = mpu.getAccX() - accelOffsetX;
    sensors.accelY = mpu.getAccY() - accelOffsetY;
    sensors.accelZ = mpu.getAccZ() - accelOffsetZ;

    sensors.gyroX = mpu.getGyroX() - gyroOffsetX;
    sensors.gyroY = mpu.getGyroY() - gyroOffsetY;
    sensors.gyroZ = mpu.getGyroZ() - gyroOffsetZ;
  }

  // Read HMC5883L with improved error handling
  if (hmc5883l_available) {
    Vector mag = compass.readNormalize();
    // Check for valid readings (HMC5883L sometimes returns 0 on error)
    if (mag.XAxis != 0 || mag.YAxis != 0 || mag.ZAxis != 0) {
      sensors.magX = mag.XAxis - magOffsetX;
      sensors.magY = mag.YAxis - magOffsetY;
      sensors.magZ = mag.ZAxis - magOffsetZ;

      // Calculate heading
      sensors.heading = atan2(sensors.magY, sensors.magX) * 180.0 / PI;
      if (sensors.heading < 0) sensors.heading += 360;

      consecutiveHMCErrors = 0;  // Reset error counter on successful read

      // Apply magnetometer calibration if available
      if (magCal.valid) {
        // Apply hard iron correction
        float magX_corrected = sensors.magX - magCal.offsetX;
        float magY_corrected = sensors.magY - magCal.offsetY;
        float magZ_corrected = sensors.magZ - magCal.offsetZ;

        // Apply soft iron correction
        magX_corrected *= magCal.scaleX;
        magY_corrected *= magCal.scaleY;
        magZ_corrected *= magCal.scaleZ;

        // Recalculate heading with corrected values
        sensors.heading = atan2(magY_corrected, magX_corrected) * 180.0 / PI;
        if (sensors.heading < 0) sensors.heading += 360;

        // Apply magnetic declination
        sensors.heading += magCal.declination;
        if (sensors.heading >= 360) sensors.heading -= 360;
        if (sensors.heading < 0) sensors.heading += 360;
      }
    } else {
      consecutiveHMCErrors++;
      sensorErrors++;
      if (sensorErrors % 100 == 0) {  // Print error every 100 failures
        Serial.print("HMC5883L read error count: ");
        Serial.println(sensorErrors);
      }

      if (consecutiveHMCErrors > 50) {  // Disable after 50 consecutive errors
        Serial.println("Too many HMC5883L errors, disabling magnetometer");
        hmc5883l_available = false;
      }
    }
  }
  
  // Use gyro for heading if magnetometer not available or disabled
  if (!hmc5883l_available) {
    static float gyroHeading = 0;
    gyroHeading += sensors.gyroZ * 0.01;  // Integrate gyro Z for heading
    if (gyroHeading < 0) gyroHeading += 360;
    if (gyroHeading >= 360) gyroHeading -= 360;
    sensors.heading = gyroHeading;
  }

  // Read MS5611 with error handling
  if (ms5611_available) {
    int result = barometer.read();
    if (result == MS5611_READ_OK) {
      sensors.pressure = barometer.getPressure();
      sensors.temperature = barometer.getTemperature();

      // Calculate altitude from pressure (standard atmosphere formula)
      sensors.altitude = 44330.0 * (1.0 - pow(sensors.pressure / baselinePressure, 0.1903));
    } else {
      sensorErrors++;
      if (sensorErrors % 100 == 0) {  // Print error every 100 failures
        Serial.print("MS5611 read error: ");
        Serial.println(result);
      }
    }
  } else {
    sensors.altitude = 0;  // Set to 0 if no barometer
  }

  // Read battery voltage (this doesn't use I2C)
  int batteryRaw = analogRead(BATTERY_PIN);
  sensors.batteryVoltage = (batteryRaw / 4095.0) * 3.3 * 4.0;  // Voltage divider
}

void readRCInputs() {
  unsigned long currentTime = millis();
  rcInput.totalReads++;

  // Read PWM signals
  rcInput.throttle = pulseIn(RC_CH1_PIN, HIGH, 25000);  // Pin 14
  rcInput.roll = pulseIn(RC_CH2_PIN, HIGH, 25000);      // Pin 13
  rcInput.pitch = pulseIn(RC_CH3_PIN, HIGH, 25000);     // Pin 12
  rcInput.yaw = pulseIn(RC_CH4_PIN, HIGH, 25000);       // Pin 27
  rcInput.aux1 = pulseIn(RC_CH5_PIN, HIGH, 25000);      // Pin 26 (Flight mode - not used)
  rcInput.aux2 = pulseIn(RC_CH6_PIN, HIGH, 25000);      // Pin 25 (Aux 1)
  rcInput.aux3 = pulseIn(RC_CH7_PIN, HIGH, 25000);      // Pin 33 (Aux 2)
  rcInput.aux4 = pulseIn(RC_CH8_PIN, HIGH, 25000);      // Pin 34 (Aux 3 - 3-position switch)



  // Check for valid signals
  bool validSignal = (rcInput.throttle > 800 && rcInput.throttle < 2200 &&
                      rcInput.roll > 800 && rcInput.roll < 2200 &&
                      rcInput.pitch > 800 && rcInput.pitch < 2200 &&
                      rcInput.yaw > 800 && rcInput.yaw < 2200);

  if (validSignal) {
    rcInput.signalLost = false;
    rcInput.lastUpdate = currentTime;
    rcInput.lastGoodSignal = currentTime;
  } else {
    rcInput.failedReads++;
    // Check if signal was lost
    if (currentTime - rcInput.lastUpdate > 1000) {  // 1 second timeout
      rcInput.signalLost = true;
    }
  }

  // Calculate signal quality (percentage of good reads in last 100 attempts)
  if (rcInput.totalReads > 0) {
    int goodReads = rcInput.totalReads - rcInput.failedReads;
    rcInput.signalQuality = (goodReads * 100) / rcInput.totalReads;

    // Reset counters every 1000 reads to prevent overflow
    if (rcInput.totalReads >= 1000) {
      rcInput.totalReads = 100;
      rcInput.failedReads = rcInput.failedReads * 100 / 1000;  // Scale down proportionally
    }
  }
}

void updateGPSData() {
  if (gps.location.isValid()) {
    sensors.latitude = gps.location.lat();
    sensors.longitude = gps.location.lng();
    sensors.gpsValid = true;
  } else {
    sensors.gpsValid = false;
  }

  if (gps.altitude.isValid()) {
    sensors.gpsAltitude = gps.altitude.meters();
  }

  sensors.satellites = gps.satellites.value();
}

// ===== FLIGHT CONTROL FUNCTIONS =====

void calculateAttitude() {
  static float rollAngle = 0, pitchAngle = 0, yawAngle = 0;
  static unsigned long lastTime = 0;
  static bool firstRun = true;

  unsigned long currentTime = micros();
  float dt = (currentTime - lastTime) / 1000000.0;

  // Skip first iteration to avoid huge dt
  if (firstRun) {
    lastTime = currentTime;
    firstRun = false;
    return;
  }
  lastTime = currentTime;

  // Complementary filter for attitude estimation
  float accelRoll = atan2(sensors.accelY, sensors.accelZ) * 180.0 / PI;
  float accelPitch = atan2(-sensors.accelX, sqrt(sensors.accelY * sensors.accelY + sensors.accelZ * sensors.accelZ)) * 180.0 / PI;

  // Integrate gyroscope data
  rollAngle += sensors.gyroX * dt;
  pitchAngle += sensors.gyroY * dt;
  yawAngle += sensors.gyroZ * dt;  // Integrate yaw from gyro Z-axis

  // Apply complementary filter (98% gyro, 2% accel for roll/pitch)
  sensors.roll = 0.98 * rollAngle + 0.02 * accelRoll;
  sensors.pitch = 0.98 * pitchAngle + 0.02 * accelPitch;

  // For yaw, use gyro integration with magnetometer correction (if available)
  if (hmc5883l_available && abs(sensors.heading) > 0.1) {
    // Use complementary filter with magnetometer for yaw drift correction
    sensors.yaw = 0.95 * yawAngle + 0.05 * sensors.heading;
  } else {
    // Pure gyro integration if magnetometer not available
    sensors.yaw = yawAngle;
  }

  // Keep yaw in 0-360 range
  if (sensors.yaw < 0) sensors.yaw += 360;
  if (sensors.yaw >= 360) sensors.yaw -= 360;

  // Update angles for next iteration
  rollAngle = sensors.roll;
  pitchAngle = sensors.pitch;
  yawAngle = sensors.yaw;
}

void runPIDControllers() {
  if (!armed) return;

  static unsigned long lastTime = 0;
  unsigned long currentTime = micros();
  float dt = (currentTime - lastTime) / 1000000.0;
  lastTime = currentTime;

  // Calculate setpoints from RC inputs
  float rollSetpoint = map(rcInput.roll, RC_MIN, RC_MAX, -30, 30);  // ±30 degrees
  float pitchSetpoint = map(rcInput.pitch, RC_MIN, RC_MAX, -30, 30);
  float yawSetpoint = map(rcInput.yaw, RC_MIN, RC_MAX, -180, 180);  // Rate mode

  // Apply deadband
  if (abs(rcInput.roll - RC_MID) < RC_DEADBAND) rollSetpoint = 0;
  if (abs(rcInput.pitch - RC_MID) < RC_DEADBAND) pitchSetpoint = 0;
  if (abs(rcInput.yaw - RC_MID) < RC_DEADBAND) yawSetpoint = 0;

  // Run PID calculations
  pidRoll.output = calculatePID(&pidRoll, rollSetpoint, sensors.roll, dt);
  pidPitch.output = calculatePID(&pidPitch, pitchSetpoint, sensors.pitch, dt);
  pidYaw.output = calculatePID(&pidYaw, yawSetpoint, sensors.gyroZ, dt);  // Rate mode

  // Altitude hold (if enabled)
  if (currentMode == ALTITUDE_HOLD || currentMode == GPS_HOLD) {
    static float altitudeSetpoint = sensors.altitude;
    if (abs(rcInput.throttle - RC_MID) > RC_DEADBAND) {
      altitudeSetpoint = sensors.altitude;  // Update setpoint when throttle moved
    }
    pidAlt.output = calculatePID(&pidAlt, altitudeSetpoint, sensors.altitude, dt);
  }
}

float calculatePID(PIDController* pid, float setpoint, float input, float dt) {
  // Update timing
  pid->lastUpdate = micros();

  // Calculate error
  float error = setpoint - input;

  // Use adaptive gains if enabled
  float kp = pid->adaptiveMode ? pid->adaptiveKp : pid->kp;
  float ki = pid->adaptiveMode ? pid->adaptiveKi : pid->ki;
  float kd = pid->adaptiveMode ? pid->adaptiveKd : pid->kd;

  // Proportional term
  float pTerm = kp * error;

  // Integral term with windup protection
  pid->integral += error * dt;
  pid->integral = constrain(pid->integral, -pid->integralMax, pid->integralMax);
  float iTerm = ki * pid->integral;

  // Derivative term (on measurement to avoid derivative kick)
  float derivative;
  if (pid->derivativeOnMeasurement) {
    derivative = -(input - pid->lastInput) / dt;  // Derivative on measurement
  } else {
    derivative = (error - pid->lastError) / dt;   // Derivative on error
  }

  // Apply derivative filtering (simple low-pass filter)
  static float lastDerivative = 0;
  derivative = lastDerivative + pid->derivativeFilter * (derivative - lastDerivative);
  lastDerivative = derivative;

  float dTerm = kd * derivative;

  // Update last values
  pid->lastError = error;
  pid->lastInput = input;

  // Calculate total output
  float output = pTerm + iTerm + dTerm;

  // Apply output limits
  output = constrain(output, pid->outputMin, pid->outputMax);

  // Update performance tracking
  pid->errorSum += error * error;  // For RMS calculation
  pid->sampleCount++;

  // Store values for tuning and analysis
  pid->setpoint = setpoint;
  pid->input = input;
  pid->output = output;

  return output;
}

void calculateMotorOutputs() {
  if (!armed) {
    motor1Speed = MOTOR_MIN;
    motor2Speed = MOTOR_MIN;
    motor3Speed = MOTOR_MIN;
    motor4Speed = MOTOR_MIN;
    return;
  }

  // Base throttle from RC input
  int baseThrottle = map(rcInput.throttle, RC_MIN, RC_MAX, MOTOR_IDLE, MOTOR_MAX);

  // Add altitude hold correction if enabled
  if (currentMode == ALTITUDE_HOLD || currentMode == GPS_HOLD) {
    baseThrottle += pidAlt.output;
  }

  // Mix PID outputs with throttle
  // Motor layout: 1=FR, 2=FL, 3=RL, 4=RR
  motor1Speed = baseThrottle - pidRoll.output + pidPitch.output - pidYaw.output;  // Front Right
  motor2Speed = baseThrottle + pidRoll.output + pidPitch.output + pidYaw.output;  // Front Left
  motor3Speed = baseThrottle + pidRoll.output - pidPitch.output - pidYaw.output;  // Rear Left
  motor4Speed = baseThrottle - pidRoll.output - pidPitch.output + pidYaw.output;  // Rear Right

  // Constrain motor speeds
  motor1Speed = constrain(motor1Speed, MOTOR_MIN, MOTOR_MAX);
  motor2Speed = constrain(motor2Speed, MOTOR_MIN, MOTOR_MAX);
  motor3Speed = constrain(motor3Speed, MOTOR_MIN, MOTOR_MAX);
  motor4Speed = constrain(motor4Speed, MOTOR_MIN, MOTOR_MAX);
}

void updateMotors() {
  motor1.writeMicroseconds(motor1Speed);
  motor2.writeMicroseconds(motor2Speed);
  motor3.writeMicroseconds(motor3Speed);
  motor4.writeMicroseconds(motor4Speed);
}

// ===== FLIGHT MODE AND SAFETY FUNCTIONS =====

void updateFlightMode() {
  // Check arming conditions
  if (!armed && rcInput.throttle < RC_MIN + 50 &&
      rcInput.yaw > RC_MAX - 50 && !rcInput.signalLost) {
    // Arm sequence: throttle low, yaw right
    armed = true;
    Serial.println("ARMED");
  } else if (armed && rcInput.throttle < RC_MIN + 50 &&
             rcInput.yaw < RC_MIN + 50) {
    // Disarm sequence: throttle low, yaw left
    armed = false;
    Serial.println("DISARMED");
  }

  // Enhanced flight mode switching based on AUX3 and AUX4 channels
  // AUX3 (3-position switch): Primary flight mode
  // AUX4 (2-position switch): Secondary mode modifier

  FlightMode newMode;

  if (rcInput.aux4 < 1400) {
    // Position 1: Basic modes
    if (rcInput.aux3 < 1400) {
      newMode = MANUAL;           // Manual mode (no stabilization)
    } else if (rcInput.aux3 < 1600) {
      newMode = STABILIZE;        // Stabilize mode (attitude stabilization)
    } else {
      newMode = ALTITUDE_HOLD;    // Altitude hold mode
    }
  } else if (rcInput.aux4 < 1600) {
    // Position 2: Advanced modes
    if (rcInput.aux3 < 1400) {
      newMode = ACRO;             // Acro mode (rate mode)
    } else if (rcInput.aux3 < 1600) {
      newMode = GPS_HOLD;         // GPS position hold
    } else {
      newMode = RETURN_TO_HOME;   // Return to home
    }
  } else {
    // Position 3: Autonomous modes
    if (rcInput.aux3 < 1400) {
      newMode = CIRCLE;           // Circle mode
    } else if (rcInput.aux3 < 1600) {
      newMode = AUTO_LAND;        // Auto landing
    } else {
      newMode = STABILIZE;        // Default to stabilize
    }
  }

  // Update current mode
  currentMode = newMode;

  // Debug flight mode changes
  static FlightMode lastMode = MANUAL;
  if (currentMode != lastMode) {
    Serial.print("Flight mode changed to: ");
    switch (currentMode) {
      case MANUAL: Serial.println("MANUAL"); break;
      case STABILIZE: Serial.println("STABILIZE"); break;
      case ALTITUDE_HOLD: Serial.println("ALTITUDE_HOLD"); break;
      case GPS_HOLD: Serial.println("GPS_HOLD"); break;
      case RETURN_TO_HOME: Serial.println("RETURN_TO_HOME"); break;
      case FAILSAFE: Serial.println("FAILSAFE"); break;
    }
    lastMode = currentMode;
  }

  // Override with failsafe if needed
  if (emergencyLanding) {
    currentMode = FAILSAFE;
  }
}

void checkFailSafe() {
  // RC signal loss
  if (rcInput.signalLost && armed) {
    if (rcLostTime == 0) {
      rcLostTime = millis();
    } else if (millis() - rcLostTime > 3000) {  // 3 second timeout
      emergencyLanding = true;
      Serial.println("FAILSAFE: RC SIGNAL LOST");
    }
  } else {
    rcLostTime = 0;
  }

  // Low battery
  if (sensors.batteryVoltage < 10.5 && armed) {  // 3S LiPo low voltage
    emergencyLanding = true;
    Serial.println("FAILSAFE: LOW BATTERY");
  }

  // Extreme attitude
  if ((abs(sensors.roll) > 60 || abs(sensors.pitch) > 60) && armed) {
    emergencyLanding = true;
    Serial.println("FAILSAFE: EXTREME ATTITUDE");
  }

  // Emergency landing procedure
  if (emergencyLanding && armed) {
    // Gradually reduce throttle
    static int emergencyThrottle = rcInput.throttle;
    emergencyThrottle -= 2;  // Reduce by 2 per loop cycle
    if (emergencyThrottle < MOTOR_IDLE) emergencyThrottle = MOTOR_IDLE;

    // Override motor outputs for controlled descent
    motor1Speed = emergencyThrottle;
    motor2Speed = emergencyThrottle;
    motor3Speed = emergencyThrottle;
    motor4Speed = emergencyThrottle;
  }
}

void setHomePosition() {
  static bool homeSet = false;
  if (!homeSet && sensors.gpsValid && sensors.satellites >= 6) {
    homeLatitude = sensors.latitude;
    homeLongitude = sensors.longitude;
    homeAltitude = sensors.altitude;
    homeSet = true;
    Serial.println("Home position set");
  }
}

// ===== CALIBRATION FUNCTIONS =====

void calibrateSensors() {
  Serial.println("Starting sensor calibration...");
  displayMessage("CALIBRATING...");

  // Check if we need full calibration or just basic setup
  bool needsFullCalibration = (!imuCal.valid || !magCal.valid || !baroCal.valid);

  if (needsFullCalibration) {
    Serial.println("Performing full sensor calibration...");

    // Calibrate accelerometer and gyroscope
    calibrateIMU();

    // Calibrate magnetometer
    calibrateMagnetometer();

    // Set baseline pressure for altitude reference
    calibrateBarometer();

    // Save all calibration data
    saveSensorCalibration();
  } else {
    Serial.println("Using saved calibration data");

    // Just update baseline pressure for current conditions
    calibrateBarometer();
  }

  Serial.println("Calibration complete!");
  displayMessage("CALIBRATION DONE");
  delay(2000);
}

void forceFullCalibration() {
  Serial.println("Forcing full sensor recalibration...");

  // Mark all calibrations as invalid to force recalibration
  imuCal.valid = false;
  magCal.valid = false;
  baroCal.valid = false;

  // Run full calibration
  calibrateSensors();
}

void calibrateIMU() {
  Serial.println("Starting enhanced IMU calibration...");
  Serial.println("This will perform 6-point accelerometer calibration");

  // Initialize calibration structure
  imuCal.accelOffsetX = 0;
  imuCal.accelOffsetY = 0;
  imuCal.accelOffsetZ = 0;
  imuCal.accelScaleX = 1.0;
  imuCal.accelScaleY = 1.0;
  imuCal.accelScaleZ = 1.0;

  // Gyroscope calibration (level position)
  Serial.println("Gyroscope calibration - Keep level and still");
  displayMessage("GYRO CAL");

  float gyroSumX = 0, gyroSumY = 0, gyroSumZ = 0;
  int samples = 1000;

  for (int i = 0; i < samples; i++) {
    mpu.update();
    gyroSumX += mpu.getGyroX();
    gyroSumY += mpu.getGyroY();
    gyroSumZ += mpu.getGyroZ();
    delay(5);
  }

  imuCal.gyroOffsetX = gyroSumX / samples;
  imuCal.gyroOffsetY = gyroSumY / samples;
  imuCal.gyroOffsetZ = gyroSumZ / samples;

  // Update legacy variables
  gyroOffsetX = imuCal.gyroOffsetX;
  gyroOffsetY = imuCal.gyroOffsetY;
  gyroOffsetZ = imuCal.gyroOffsetZ;

  // 6-point accelerometer calibration
  float accelReadings[6][3];  // 6 orientations, 3 axes
  const char* orientations[] = {
    "Level (Z up)",
    "Upside down (Z down)",
    "Right side (Y up)",
    "Left side (Y down)",
    "Nose up (X up)",
    "Nose down (X down)"
  };

  for (int orientation = 0; orientation < 6; orientation++) {
    Serial.print("Position "); Serial.print(orientation + 1); Serial.print("/6: ");
    Serial.println(orientations[orientation]);
    Serial.println("Press AUX1 when ready...");

    displayMessage(orientations[orientation]);

    // Wait for AUX1 press
    while (rcInput.aux1 < 1700) {
      readRCInputs();
      delay(100);
    }

    // Wait for AUX1 release
    while (rcInput.aux1 > 1300) {
      readRCInputs();
      delay(100);
    }

    Serial.println("Measuring...");
    delay(1000);  // Settle time

    // Take measurements
    float sumX = 0, sumY = 0, sumZ = 0;
    for (int i = 0; i < 200; i++) {
      mpu.update();
      sumX += mpu.getAccX();
      sumY += mpu.getAccY();
      sumZ += mpu.getAccZ();
      delay(10);
    }

    accelReadings[orientation][0] = sumX / 200.0;
    accelReadings[orientation][1] = sumY / 200.0;
    accelReadings[orientation][2] = sumZ / 200.0;

    Serial.print("Readings: X="); Serial.print(accelReadings[orientation][0]);
    Serial.print(" Y="); Serial.print(accelReadings[orientation][1]);
    Serial.print(" Z="); Serial.println(accelReadings[orientation][2]);
  }

  // Calculate offsets and scales
  imuCal.accelOffsetX = (accelReadings[4][0] + accelReadings[5][0]) / 2.0;  // X up + X down
  imuCal.accelOffsetY = (accelReadings[2][1] + accelReadings[3][1]) / 2.0;  // Y up + Y down
  imuCal.accelOffsetZ = (accelReadings[0][2] + accelReadings[1][2]) / 2.0;  // Z up + Z down

  imuCal.accelScaleX = 2.0 / (accelReadings[4][0] - accelReadings[5][0]);  // 2g / range
  imuCal.accelScaleY = 2.0 / (accelReadings[2][1] - accelReadings[3][1]);
  imuCal.accelScaleZ = 2.0 / (accelReadings[0][2] - accelReadings[1][2]);

  // Update legacy variables
  accelOffsetX = imuCal.accelOffsetX;
  accelOffsetY = imuCal.accelOffsetY;
  accelOffsetZ = imuCal.accelOffsetZ;

  // Store reference temperature
  imuCal.temperature = sensors.temperature;
  imuCal.valid = true;

  Serial.println("Enhanced IMU calibration complete!");
  Serial.print("Accel offsets: X="); Serial.print(imuCal.accelOffsetX);
  Serial.print(" Y="); Serial.print(imuCal.accelOffsetY);
  Serial.print(" Z="); Serial.println(imuCal.accelOffsetZ);
  Serial.print("Accel scales: X="); Serial.print(imuCal.accelScaleX);
  Serial.print(" Y="); Serial.print(imuCal.accelScaleY);
  Serial.print(" Z="); Serial.println(imuCal.accelScaleZ);
}

void calibrateMagnetometer() {
  Serial.println("Enhanced magnetometer calibration starting...");
  Serial.println("Rotate quadcopter in all directions for 60 seconds");
  Serial.println("Try to cover all orientations smoothly");

  displayMessage("MAG CAL - ROTATE");

  // Data collection arrays
  const int maxSamples = 1000;
  float magData[maxSamples][3];
  int sampleCount = 0;

  float magMinX = 1000, magMaxX = -1000;
  float magMinY = 1000, magMaxY = -1000;
  float magMinZ = 1000, magMaxZ = -1000;

  unsigned long startTime = millis();
  unsigned long lastSample = 0;

  while (millis() - startTime < 60000 && sampleCount < maxSamples) {  // 60 second calibration
    if (millis() - lastSample > 60) {  // Sample every 60ms
      Vector mag = compass.readNormalize();
      if (mag.XAxis != 0 || mag.YAxis != 0 || mag.ZAxis != 0) {
        float x = mag.XAxis;
        float y = mag.YAxis;
        float z = mag.ZAxis;

        // Store sample for sphere fitting
        if (sampleCount < maxSamples) {
          magData[sampleCount][0] = x;
          magData[sampleCount][1] = y;
          magData[sampleCount][2] = z;
          sampleCount++;
        }

        // Track min/max for basic calibration
        if (x < magMinX) magMinX = x;
        if (x > magMaxX) magMaxX = x;
        if (y < magMinY) magMinY = y;
        if (y > magMaxY) magMaxY = y;
        if (z < magMinZ) magMinZ = z;
        if (z > magMaxZ) magMaxZ = z;

        lastSample = millis();

        // Progress indicator
        if (sampleCount % 50 == 0) {
          Serial.print("Samples: "); Serial.println(sampleCount);
        }
      }
    }
    delay(10);
  }

  Serial.print("Collected "); Serial.print(sampleCount); Serial.println(" samples");

  // Basic calibration (hard iron correction)
  magCal.offsetX = (magMaxX + magMinX) / 2.0;
  magCal.offsetY = (magMaxY + magMinY) / 2.0;
  magCal.offsetZ = (magMaxZ + magMinZ) / 2.0;

  // Simple soft iron correction (ellipsoid to sphere)
  float rangeX = magMaxX - magMinX;
  float rangeY = magMaxY - magMinY;
  float rangeZ = magMaxZ - magMinZ;
  float avgRange = (rangeX + rangeY + rangeZ) / 3.0;

  magCal.scaleX = avgRange / rangeX;
  magCal.scaleY = avgRange / rangeY;
  magCal.scaleZ = avgRange / rangeZ;

  // Set magnetic declination (you should update this for your location)
  // This is a placeholder - get actual declination from: https://www.magnetic-declination.com/
  magCal.declination = 0.0;  // Update for your location

  // Update legacy variables
  magOffsetX = magCal.offsetX;
  magOffsetY = magCal.offsetY;
  magOffsetZ = magCal.offsetZ;

  magCal.valid = true;

  Serial.println("Enhanced magnetometer calibration complete!");
  Serial.print("Hard iron offsets: X="); Serial.print(magCal.offsetX);
  Serial.print(" Y="); Serial.print(magCal.offsetY);
  Serial.print(" Z="); Serial.println(magCal.offsetZ);
  Serial.print("Soft iron scales: X="); Serial.print(magCal.scaleX);
  Serial.print(" Y="); Serial.print(magCal.scaleY);
  Serial.print(" Z="); Serial.println(magCal.scaleZ);
  Serial.println("Note: Update magnetic declination for your location!");
}

// ===== PID TUNING AND PERFORMANCE FUNCTIONS =====

void startPIDTuning(PIDTuningMode mode) {
  tuningMode = mode;
  tuningActive = true;
  tuningStartTime = millis();

  const char* modeNames[] = {"OFF", "ROLL", "PITCH", "YAW", "ALTITUDE"};
  Serial.print("Starting PID tuning for: ");
  Serial.println(modeNames[mode]);
  Serial.println("Use AUX1/AUX2 to adjust P/I/D gains");
  Serial.println("AUX3 to switch between P/I/D");
  Serial.println("AUX4 high to save and exit");

  displayMessage("PID TUNING");
}

void updatePIDTuning() {
  if (!tuningActive) return;

  PIDController* activePID = nullptr;
  const char* pidName = "";

  // Select active PID controller
  switch (tuningMode) {
    case TUNING_ROLL:
      activePID = &pidRoll;
      pidName = "ROLL";
      break;
    case TUNING_PITCH:
      activePID = &pidPitch;
      pidName = "PITCH";
      break;
    case TUNING_YAW:
      activePID = &pidYaw;
      pidName = "YAW";
      break;
    case TUNING_ALTITUDE:
      activePID = &pidAlt;
      pidName = "ALT";
      break;
    default:
      return;
  }

  // Determine which gain to adjust based on AUX3
  float* activeGain = nullptr;
  const char* gainName = "";

  if (rcInput.aux3 < 1400) {
    activeGain = &activePID->kp;
    gainName = "P";
  } else if (rcInput.aux3 < 1600) {
    activeGain = &activePID->ki;
    gainName = "I";
  } else {
    activeGain = &activePID->kd;
    gainName = "D";
  }

  // Adjust gain based on AUX1 and AUX2
  static unsigned long lastAdjustment = 0;
  if (millis() - lastAdjustment > 200) {  // Limit adjustment rate
    if (rcInput.aux1 > 1700) {
      *activeGain += tuningStep;
      lastAdjustment = millis();
    } else if (rcInput.aux2 > 1700) {
      *activeGain -= tuningStep;
      if (*activeGain < 0) *activeGain = 0;
      lastAdjustment = millis();
    }
  }

  // Print current values
  static unsigned long lastPrint = 0;
  if (millis() - lastPrint > 1000) {
    Serial.print(pidName); Serial.print(" "); Serial.print(gainName);
    Serial.print(": "); Serial.print(*activeGain, 3);
    Serial.print(" (P:"); Serial.print(activePID->kp, 3);
    Serial.print(" I:"); Serial.print(activePID->ki, 3);
    Serial.print(" D:"); Serial.print(activePID->kd, 3); Serial.println(")");
    lastPrint = millis();
  }

  // Exit tuning mode
  if (rcInput.aux4 > 1700) {
    tuningActive = false;
    tuningMode = TUNING_OFF;
    savePIDSettings();
    Serial.println("PID tuning complete - settings saved");
    displayMessage("PID SAVED");
    delay(2000);
  }
}

void savePIDSettings() {
  preferences.begin("pid_settings", false);

  // Save Roll PID
  preferences.putFloat("roll_kp", pidRoll.kp);
  preferences.putFloat("roll_ki", pidRoll.ki);
  preferences.putFloat("roll_kd", pidRoll.kd);

  // Save Pitch PID
  preferences.putFloat("pitch_kp", pidPitch.kp);
  preferences.putFloat("pitch_ki", pidPitch.ki);
  preferences.putFloat("pitch_kd", pidPitch.kd);

  // Save Yaw PID
  preferences.putFloat("yaw_kp", pidYaw.kp);
  preferences.putFloat("yaw_ki", pidYaw.ki);
  preferences.putFloat("yaw_kd", pidYaw.kd);

  // Save Altitude PID
  preferences.putFloat("alt_kp", pidAlt.kp);
  preferences.putFloat("alt_ki", pidAlt.ki);
  preferences.putFloat("alt_kd", pidAlt.kd);

  preferences.end();
}

void loadPIDSettings() {
  preferences.begin("pid_settings", true);

  // Load Roll PID
  pidRoll.kp = preferences.getFloat("roll_kp", PID_ROLL_KP);
  pidRoll.ki = preferences.getFloat("roll_ki", PID_ROLL_KI);
  pidRoll.kd = preferences.getFloat("roll_kd", PID_ROLL_KD);

  // Load Pitch PID
  pidPitch.kp = preferences.getFloat("pitch_kp", PID_PITCH_KP);
  pidPitch.ki = preferences.getFloat("pitch_ki", PID_PITCH_KI);
  pidPitch.kd = preferences.getFloat("pitch_kd", PID_PITCH_KD);

  // Load Yaw PID
  pidYaw.kp = preferences.getFloat("yaw_kp", PID_YAW_KP);
  pidYaw.ki = preferences.getFloat("yaw_ki", PID_YAW_KI);
  pidYaw.kd = preferences.getFloat("yaw_kd", PID_YAW_KD);

  // Load Altitude PID
  pidAlt.kp = preferences.getFloat("alt_kp", PID_ALT_KP);
  pidAlt.ki = preferences.getFloat("alt_ki", PID_ALT_KI);
  pidAlt.kd = preferences.getFloat("alt_kd", PID_ALT_KD);

  preferences.end();

  Serial.println("PID settings loaded from storage");
}

void updatePerformanceMetrics() {
  static unsigned long lastUpdate = 0;
  static unsigned long loopCount = 0;
  static unsigned long totalLoopTime = 0;

  unsigned long currentTime = micros();

  if (lastUpdate > 0) {
    unsigned long loopTime = currentTime - lastUpdate;
    totalLoopTime += loopTime;
    loopCount++;

    // Check for loop time violations (target: 2500µs = 400Hz)
    if (loopTime > 2500) {
      performance.loopTimeViolations++;
    }

    // Calculate average loop time every 100 loops
    if (loopCount >= 100) {
      performance.loopTime = totalLoopTime / loopCount;
      totalLoopTime = 0;
      loopCount = 0;
    }
  }

  lastUpdate = currentTime;

  // Update flight time when armed
  static unsigned long armTime = 0;
  if (armed && armTime == 0) {
    armTime = millis();
  } else if (!armed && armTime > 0) {
    performance.flightTime += millis() - armTime;
    armTime = 0;
  }

  // Calculate RMS errors every 1000 samples
  static int updateCounter = 0;
  updateCounter++;

  if (updateCounter >= 1000) {
    if (pidRoll.sampleCount > 0) {
      performance.rollRMS = sqrt(pidRoll.errorSum / pidRoll.sampleCount);
      pidRoll.errorSum = 0;
      pidRoll.sampleCount = 0;
    }

    if (pidPitch.sampleCount > 0) {
      performance.pitchRMS = sqrt(pidPitch.errorSum / pidPitch.sampleCount);
      pidPitch.errorSum = 0;
      pidPitch.sampleCount = 0;
    }

    if (pidYaw.sampleCount > 0) {
      performance.yawRMS = sqrt(pidYaw.errorSum / pidYaw.sampleCount);
      pidYaw.errorSum = 0;
      pidYaw.sampleCount = 0;
    }

    if (pidAlt.sampleCount > 0) {
      performance.altitudeRMS = sqrt(pidAlt.errorSum / pidAlt.sampleCount);
      pidAlt.errorSum = 0;
      pidAlt.sampleCount = 0;
    }

    updateCounter = 0;
  }
}

void detectVibration() {
  static float accelHistory[10];
  static int historyIndex = 0;
  static bool historyFull = false;

  // Calculate total acceleration magnitude
  float totalAccel = sqrt(sensors.accelX * sensors.accelX +
                         sensors.accelY * sensors.accelY +
                         sensors.accelZ * sensors.accelZ);

  // Store in circular buffer
  accelHistory[historyIndex] = totalAccel;
  historyIndex = (historyIndex + 1) % 10;
  if (historyIndex == 0) historyFull = true;

  if (historyFull) {
    // Calculate standard deviation of acceleration
    float mean = 0;
    for (int i = 0; i < 10; i++) {
      mean += accelHistory[i];
    }
    mean /= 10.0;

    float variance = 0;
    for (int i = 0; i < 10; i++) {
      float diff = accelHistory[i] - mean;
      variance += diff * diff;
    }
    variance /= 10.0;

    performance.vibrationLevel = sqrt(variance);

    // Warn if vibration is too high
    if (performance.vibrationLevel > 2.0) {  // Threshold for high vibration
      static unsigned long lastWarning = 0;
      if (millis() - lastWarning > 5000) {  // Warn every 5 seconds
        Serial.print("WARNING: High vibration detected: ");
        Serial.println(performance.vibrationLevel);
        lastWarning = millis();
      }
    }
  }
}

void printPerformanceReport() {
  Serial.println("=== FLIGHT PERFORMANCE REPORT ===");
  Serial.print("Flight Time: "); Serial.print(performance.flightTime / 1000.0); Serial.println(" seconds");
  Serial.print("Average Loop Time: "); Serial.print(performance.loopTime); Serial.println(" µs");
  Serial.print("Loop Time Violations: "); Serial.println(performance.loopTimeViolations);
  Serial.print("Roll RMS Error: "); Serial.print(performance.rollRMS); Serial.println("°");
  Serial.print("Pitch RMS Error: "); Serial.print(performance.pitchRMS); Serial.println("°");
  Serial.print("Yaw RMS Error: "); Serial.print(performance.yawRMS); Serial.println("°");
  Serial.print("Altitude RMS Error: "); Serial.print(performance.altitudeRMS); Serial.println(" m");
  Serial.print("Vibration Level: "); Serial.println(performance.vibrationLevel);
  Serial.println("==================================");
}

// ===== ENHANCED DISPLAY FUNCTIONS =====

void drawArtificialHorizon() {
  tft.fillScreen(ST7735_BLACK);

  // Display parameters
  int centerX = 64;  // Screen center X
  int centerY = 64;  // Screen center Y
  int radius = 50;   // Horizon circle radius

  // Calculate horizon line based on roll and pitch
  float rollRad = sensors.roll * PI / 180.0;
  float pitchRad = sensors.pitch * PI / 180.0;

  // Draw sky (upper half)
  tft.fillCircle(centerX, centerY, radius, ST7735_BLUE);

  // Calculate horizon line points
  int horizonY = centerY + (int)(sin(pitchRad) * 30);  // Pitch offset

  // Draw ground (lower half) - create polygon for ground
  for (int y = horizonY; y < centerY + radius; y++) {
    int width = sqrt(radius * radius - (y - centerY) * (y - centerY));
    tft.drawFastHLine(centerX - width, y, 2 * width, ST7735_GREEN);
  }

  // Draw horizon line
  int x1 = centerX - cos(rollRad) * radius;
  int y1 = horizonY - sin(rollRad) * 10;
  int x2 = centerX + cos(rollRad) * radius;
  int y2 = horizonY + sin(rollRad) * 10;
  tft.drawLine(x1, y1, x2, y2, ST7735_WHITE);

  // Draw aircraft symbol (fixed center)
  tft.drawFastHLine(centerX - 15, centerY, 30, ST7735_YELLOW);
  tft.drawFastVLine(centerX, centerY - 5, 10, ST7735_YELLOW);
  tft.fillCircle(centerX, centerY, 2, ST7735_YELLOW);

  // Draw attitude values
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(5, 5);
  tft.print("R:"); tft.print(sensors.roll, 1);
  tft.setCursor(5, 15);
  tft.print("P:"); tft.print(sensors.pitch, 1);
  tft.setCursor(5, 25);
  tft.print("Y:"); tft.print(sensors.yaw, 1);

  // Draw altitude and heading
  tft.setCursor(80, 5);
  tft.print("Alt:");
  tft.setCursor(80, 15);
  tft.print(sensors.altitude, 1);
  tft.setCursor(80, 25);
  tft.print("Hdg:");
  tft.setCursor(80, 35);
  tft.print(sensors.heading, 0);
}

void drawGPSMap() {
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);

  // Title
  tft.setCursor(30, 5);
  tft.println("GPS MAP");

  if (gps.location.isValid()) {
    // Current position
    tft.setCursor(5, 20);
    tft.print("Lat: "); tft.println(gps.location.lat(), 6);
    tft.setCursor(5, 30);
    tft.print("Lng: "); tft.println(gps.location.lng(), 6);

    // Distance and bearing to home
    if (gpsTracker.homeSet) {
      tft.setCursor(5, 45);
      tft.print("Dist: "); tft.print(gpsTracker.currentDistance, 1); tft.println("m");
      tft.setCursor(5, 55);
      tft.print("Bear: "); tft.print(gpsTracker.bearing, 0); tft.println("°");

      // Simple map representation
      int mapCenterX = 64;
      int mapCenterY = 90;
      int mapRadius = 25;

      // Draw map circle
      tft.drawCircle(mapCenterX, mapCenterY, mapRadius, ST7735_WHITE);

      // Draw home position (center)
      tft.fillCircle(mapCenterX, mapCenterY, 2, ST7735_GREEN);
      tft.setCursor(mapCenterX - 10, mapCenterY + 8);
      tft.setTextColor(ST7735_GREEN);
      tft.print("H");

      // Draw current position
      if (gpsTracker.currentDistance < 100) {  // Only if within 100m
        float scale = gpsTracker.currentDistance / 100.0 * mapRadius;
        float bearingRad = gpsTracker.bearing * PI / 180.0;
        int currentX = mapCenterX + sin(bearingRad) * scale;
        int currentY = mapCenterY - cos(bearingRad) * scale;
        tft.fillCircle(currentX, currentY, 2, ST7735_RED);
      }
    }

    // GPS status
    tft.setTextColor(ST7735_WHITE);
    tft.setCursor(5, 70);
    tft.print("Sats: "); tft.println(gps.satellites.value());
    tft.setCursor(5, 80);
    tft.print("HDOP: "); tft.println(gps.hdop.value() / 100.0, 1);
  } else {
    tft.setCursor(20, 40);
    tft.setTextColor(ST7735_RED);
    tft.println("NO GPS FIX");
  }
}

void drawPerformanceDisplay() {
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);

  // Title
  tft.setCursor(20, 5);
  tft.println("PERFORMANCE");

  // Loop time
  tft.setCursor(5, 20);
  tft.print("Loop: "); tft.print(performance.loopTime); tft.println("us");

  // RMS errors
  tft.setCursor(5, 30);
  tft.print("Roll RMS: "); tft.println(performance.rollRMS, 2);
  tft.setCursor(5, 40);
  tft.print("Pitch RMS: "); tft.println(performance.pitchRMS, 2);
  tft.setCursor(5, 50);
  tft.print("Yaw RMS: "); tft.println(performance.yawRMS, 2);

  // Vibration level
  tft.setCursor(5, 65);
  tft.print("Vibration: ");
  if (performance.vibrationLevel > 2.0) {
    tft.setTextColor(ST7735_RED);
  } else if (performance.vibrationLevel > 1.0) {
    tft.setTextColor(ST7735_YELLOW);
  } else {
    tft.setTextColor(ST7735_GREEN);
  }
  tft.println(performance.vibrationLevel, 2);

  // Flight time
  tft.setTextColor(ST7735_WHITE);
  tft.setCursor(5, 80);
  tft.print("Flight: "); tft.print(performance.flightTime / 1000); tft.println("s");

  // Loop violations
  tft.setCursor(5, 95);
  tft.print("Violations: "); tft.println(performance.loopTimeViolations);

  // PID tuning status
  if (tuningActive) {
    tft.setCursor(5, 110);
    tft.setTextColor(ST7735_YELLOW);
    tft.print("TUNING: ");
    switch (tuningMode) {
      case TUNING_ROLL: tft.println("ROLL"); break;
      case TUNING_PITCH: tft.println("PITCH"); break;
      case TUNING_YAW: tft.println("YAW"); break;
      case TUNING_ALTITUDE: tft.println("ALT"); break;
      default: break;
    }
  }
}

void drawDiagnosticsDisplay() {
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);

  // Title
  tft.setCursor(15, 5);
  tft.println("DIAGNOSTICS");

  // Sensor status
  tft.setCursor(5, 20);
  tft.print("MPU6050: ");
  tft.setTextColor(mpu6050_available ? ST7735_GREEN : ST7735_RED);
  tft.println(mpu6050_available ? "OK" : "FAIL");

  tft.setTextColor(ST7735_WHITE);
  tft.setCursor(5, 30);
  tft.print("HMC5883L: ");
  tft.setTextColor(hmc5883l_available ? ST7735_GREEN : ST7735_RED);
  tft.println(hmc5883l_available ? "OK" : "FAIL");

  tft.setTextColor(ST7735_WHITE);
  tft.setCursor(5, 40);
  tft.print("MS5611: ");
  tft.setTextColor(ms5611_available ? ST7735_GREEN : ST7735_RED);
  tft.println(ms5611_available ? "OK" : "FAIL");

  // RC signal quality
  tft.setTextColor(ST7735_WHITE);
  tft.setCursor(5, 55);
  tft.print("RC Quality: ");
  if (rcInput.signalQuality >= 90) {
    tft.setTextColor(ST7735_GREEN);
  } else if (rcInput.signalQuality >= 70) {
    tft.setTextColor(ST7735_YELLOW);
  } else {
    tft.setTextColor(ST7735_RED);
  }
  tft.print(rcInput.signalQuality); tft.println("%");

  // Battery status
  tft.setTextColor(ST7735_WHITE);
  tft.setCursor(5, 70);
  tft.print("Battery: ");
  if (sensors.batteryVoltage > 11.5) {
    tft.setTextColor(ST7735_GREEN);
  } else if (sensors.batteryVoltage > 10.5) {
    tft.setTextColor(ST7735_YELLOW);
  } else {
    tft.setTextColor(ST7735_RED);
  }
  tft.print(sensors.batteryVoltage, 1); tft.println("V");

  // Data logging status
  tft.setTextColor(ST7735_WHITE);
  tft.setCursor(5, 85);
  tft.print("Logging: ");
  tft.setTextColor(dataLogger.enabled ? ST7735_GREEN : ST7735_RED);
  tft.println(dataLogger.enabled ? "ON" : "OFF");

  // WiFi telemetry status
  tft.setTextColor(ST7735_WHITE);
  tft.setCursor(5, 100);
  tft.print("WiFi: ");
  tft.setTextColor(telemetry.wifiEnabled ? ST7735_GREEN : ST7735_RED);
  tft.println(telemetry.wifiEnabled ? "ON" : "OFF");
}

// ===== DATA LOGGING SYSTEM =====

void initDataLogger() {
  dataLogger.enabled = false;
  dataLogger.sdCardAvailable = false;
  dataLogger.logInterval = 100;  // 10Hz logging
  dataLogger.flightNumber = 1;
  dataLogger.lastLogTime = 0;
  dataLogger.sessionStartTime = millis();

  // Try to initialize SPIFFS for internal storage
  if (SPIFFS.begin(true)) {
    Serial.println("SPIFFS initialized for data logging");
    dataLogger.enabled = true;

    // Create new log file
    createNewLogFile();
  } else {
    Serial.println("SPIFFS initialization failed - logging disabled");
  }
}

void createNewLogFile() {
  // Generate unique filename with timestamp
  sprintf(dataLogger.currentLogFileName, "/flight_%03d.csv", dataLogger.flightNumber);

  // Check if file exists and increment number
  while (SPIFFS.exists(dataLogger.currentLogFileName)) {
    dataLogger.flightNumber++;
    sprintf(dataLogger.currentLogFileName, "/flight_%03d.csv", dataLogger.flightNumber);
  }

  // Create and open new log file
  dataLogger.logFile = SPIFFS.open(dataLogger.currentLogFileName, FILE_WRITE);
  if (dataLogger.logFile) {
    // Write CSV header
    dataLogger.logFile.println("Time,Armed,Mode,Roll,Pitch,Yaw,Altitude,Throttle,RollRC,PitchRC,YawRC,BattVolt,GPSLat,GPSLng,GPSSats,Vibration");
    dataLogger.logFile.flush();

    Serial.print("Created log file: ");
    Serial.println(dataLogger.currentLogFileName);
  } else {
    Serial.println("Failed to create log file");
    dataLogger.enabled = false;
  }
}

void logFlightData() {
  if (!dataLogger.enabled || !dataLogger.logFile) return;

  unsigned long currentTime = millis();
  if (currentTime - dataLogger.lastLogTime < dataLogger.logInterval) return;

  dataLogger.lastLogTime = currentTime;

  // Create CSV data line
  String logLine = "";
  logLine += String(currentTime - dataLogger.sessionStartTime) + ",";  // Time
  logLine += String(armed ? 1 : 0) + ",";                              // Armed
  logLine += String(currentMode) + ",";                                // Mode
  logLine += String(sensors.roll, 2) + ",";                           // Roll
  logLine += String(sensors.pitch, 2) + ",";                          // Pitch
  logLine += String(sensors.yaw, 2) + ",";                            // Yaw
  logLine += String(sensors.altitude, 2) + ",";                       // Altitude
  logLine += String(rcInput.throttle) + ",";                          // Throttle
  logLine += String(rcInput.roll) + ",";                              // Roll RC
  logLine += String(rcInput.pitch) + ",";                             // Pitch RC
  logLine += String(rcInput.yaw) + ",";                               // Yaw RC
  logLine += String(sensors.batteryVoltage, 2) + ",";                 // Battery

  // GPS data
  if (gps.location.isValid()) {
    logLine += String(gps.location.lat(), 6) + ",";                   // GPS Lat
    logLine += String(gps.location.lng(), 6) + ",";                   // GPS Lng
  } else {
    logLine += "0.0,0.0,";
  }
  logLine += String(gps.satellites.value()) + ",";                    // GPS Sats
  logLine += String(performance.vibrationLevel, 3);                   // Vibration

  // Write to file
  dataLogger.logFile.println(logLine);

  // Flush every 10 writes to ensure data is saved
  static int writeCount = 0;
  writeCount++;
  if (writeCount >= 10) {
    dataLogger.logFile.flush();
    writeCount = 0;
  }
}

void closeLogFile() {
  if (dataLogger.logFile) {
    dataLogger.logFile.flush();
    dataLogger.logFile.close();
    Serial.print("Log file closed: ");
    Serial.println(dataLogger.currentLogFileName);
  }
}

void listLogFiles() {
  Serial.println("=== FLIGHT LOG FILES ===");
  File root = SPIFFS.open("/");
  File file = root.openNextFile();

  while (file) {
    if (String(file.name()).endsWith(".csv")) {
      Serial.print("File: ");
      Serial.print(file.name());
      Serial.print(" Size: ");
      Serial.print(file.size());
      Serial.println(" bytes");
    }
    file = root.openNextFile();
  }
  Serial.println("========================");
}

// ===== WIFI TELEMETRY SYSTEM =====

void initWiFiTelemetry() {
  telemetry.wifiEnabled = false;
  telemetry.clientConnected = false;
  telemetry.telemetryInterval = 200;  // 5Hz telemetry
  telemetry.lastTelemetryTime = 0;

  // Set up as Access Point
  strcpy(telemetry.ssid, "QuadFly_Telemetry");
  strcpy(telemetry.password, "quadfly123");

  WiFi.mode(WIFI_AP);
  WiFi.softAP(telemetry.ssid, telemetry.password);
  telemetry.localIP = WiFi.softAPIP();

  // Initialize web server
  telemetry.webServer = new WebServer(80);

  // Set up web server routes
  telemetry.webServer->on("/", handleRoot);
  telemetry.webServer->on("/telemetry", handleTelemetry);
  telemetry.webServer->on("/logs", handleLogs);
  telemetry.webServer->on("/config", handleConfig);
  telemetry.webServer->onNotFound(handleNotFound);

  telemetry.webServer->begin();
  telemetry.wifiEnabled = true;

  Serial.println("WiFi Telemetry System Started");
  Serial.print("SSID: ");
  Serial.println(telemetry.ssid);
  Serial.print("IP Address: ");
  Serial.println(telemetry.localIP);
  Serial.println("Web interface available at http://***********");
}

void handleRoot() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<title>QuadFly Flight Controller</title>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1'>";
  html += "<style>body{font-family:Arial;margin:20px;background:#f0f0f0;}";
  html += ".container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:10px;}";
  html += ".status{padding:10px;margin:10px 0;border-radius:5px;}";
  html += ".armed{background:#ffcccc;}.disarmed{background:#ccffcc;}";
  html += ".data{display:grid;grid-template-columns:1fr 1fr;gap:10px;}";
  html += ".card{background:#f9f9f9;padding:15px;border-radius:5px;border-left:4px solid #007acc;}";
  html += "</style></head><body>";

  html += "<div class='container'>";
  html += "<h1>🚁 QuadFly Flight Controller</h1>";

  // Status indicator
  html += "<div class='status " + String(armed ? "armed" : "disarmed") + "'>";
  html += "<h2>Status: " + String(armed ? "ARMED" : "DISARMED") + "</h2>";
  html += "<p>Flight Mode: " + getFlightModeString() + "</p>";
  html += "</div>";

  // Real-time data
  html += "<div class='data'>";
  html += "<div class='card'><h3>Attitude</h3>";
  html += "<p>Roll: " + String(sensors.roll, 1) + "°</p>";
  html += "<p>Pitch: " + String(sensors.pitch, 1) + "°</p>";
  html += "<p>Yaw: " + String(sensors.yaw, 1) + "°</p></div>";

  html += "<div class='card'><h3>Position</h3>";
  html += "<p>Altitude: " + String(sensors.altitude, 1) + "m</p>";
  if (gps.location.isValid()) {
    html += "<p>GPS: " + String(gps.location.lat(), 6) + ", " + String(gps.location.lng(), 6) + "</p>";
    html += "<p>Satellites: " + String(gps.satellites.value()) + "</p>";
  } else {
    html += "<p>GPS: No Fix</p>";
  }
  html += "</div>";

  html += "<div class='card'><h3>System</h3>";
  html += "<p>Battery: " + String(sensors.batteryVoltage, 1) + "V</p>";
  html += "<p>RC Quality: " + String(rcInput.signalQuality) + "%</p>";
  html += "<p>Vibration: " + String(performance.vibrationLevel, 2) + "</p></div>";

  html += "<div class='card'><h3>Performance</h3>";
  html += "<p>Loop Time: " + String(performance.loopTime) + "µs</p>";
  html += "<p>Flight Time: " + String(performance.flightTime / 1000) + "s</p>";
  html += "<p>Violations: " + String(performance.loopTimeViolations) + "</p></div>";
  html += "</div>";

  // Navigation
  html += "<div style='margin-top:20px;'>";
  html += "<a href='/telemetry' style='margin-right:10px;'>📊 Live Telemetry</a>";
  html += "<a href='/logs' style='margin-right:10px;'>📁 Flight Logs</a>";
  html += "<a href='/config'>⚙️ Configuration</a>";
  html += "</div>";

  html += "</div>";
  html += "<script>setTimeout(function(){location.reload();}, 2000);</script>";  // Auto-refresh
  html += "</body></html>";

  telemetry.webServer->send(200, "text/html", html);
}

void handleTelemetry() {
  // Create JSON telemetry data
  DynamicJsonDocument doc(1024);

  doc["timestamp"] = millis();
  doc["armed"] = armed;
  doc["mode"] = currentMode;
  doc["roll"] = sensors.roll;
  doc["pitch"] = sensors.pitch;
  doc["yaw"] = sensors.yaw;
  doc["altitude"] = sensors.altitude;
  doc["battery"] = sensors.batteryVoltage;
  doc["rcQuality"] = rcInput.signalQuality;
  doc["vibration"] = performance.vibrationLevel;
  doc["loopTime"] = performance.loopTime;

  if (gps.location.isValid()) {
    doc["gpsLat"] = gps.location.lat();
    doc["gpsLng"] = gps.location.lng();
    doc["gpsSats"] = gps.satellites.value();
  }

  String jsonString;
  serializeJson(doc, jsonString);

  telemetry.webServer->send(200, "application/json", jsonString);
}

void handleLogs() {
  String html = "<!DOCTYPE html><html><head><title>Flight Logs</title></head><body>";
  html += "<h1>Flight Log Files</h1>";

  File root = SPIFFS.open("/");
  File file = root.openNextFile();

  while (file) {
    if (String(file.name()).endsWith(".csv")) {
      html += "<p><a href='/download?file=" + String(file.name()) + "'>";
      html += String(file.name()) + "</a> (" + String(file.size()) + " bytes)</p>";
    }
    file = root.openNextFile();
  }

  html += "<p><a href='/'>← Back to Dashboard</a></p>";
  html += "</body></html>";

  telemetry.webServer->send(200, "text/html", html);
}

void handleConfig() {
  String html = "<!DOCTYPE html><html><head><title>Configuration</title></head><body>";
  html += "<h1>Flight Controller Configuration</h1>";
  html += "<h2>PID Settings</h2>";
  html += "<p>Roll - P:" + String(pidRoll.kp, 3) + " I:" + String(pidRoll.ki, 3) + " D:" + String(pidRoll.kd, 3) + "</p>";
  html += "<p>Pitch - P:" + String(pidPitch.kp, 3) + " I:" + String(pidPitch.ki, 3) + " D:" + String(pidPitch.kd, 3) + "</p>";
  html += "<p>Yaw - P:" + String(pidYaw.kp, 3) + " I:" + String(pidYaw.ki, 3) + " D:" + String(pidYaw.kd, 3) + "</p>";
  html += "<p><a href='/'>← Back to Dashboard</a></p>";
  html += "</body></html>";

  telemetry.webServer->send(200, "text/html", html);
}

void handleNotFound() {
  telemetry.webServer->send(404, "text/plain", "File not found");
}

String getFlightModeString() {
  switch (currentMode) {
    case MANUAL: return "MANUAL";
    case STABILIZE: return "STABILIZE";
    case ALTITUDE_HOLD: return "ALTITUDE HOLD";
    case GPS_HOLD: return "GPS HOLD";
    case RETURN_TO_HOME: return "RETURN TO HOME";
    case ACRO: return "ACRO";
    case CIRCLE: return "CIRCLE";
    case AUTO_LAND: return "AUTO LAND";
    case FAILSAFE: return "FAILSAFE";
    default: return "UNKNOWN";
  }
}

void updateTelemetry() {
  if (!telemetry.wifiEnabled) return;

  // Handle web server requests
  telemetry.webServer->handleClient();

  // Update connection status
  telemetry.clientConnected = (WiFi.softAPgetStationNum() > 0);
}

// ===== GPS TRACKING FUNCTIONS =====

void initGPSTracker() {
  gpsTracker.homeSet = false;
  gpsTracker.homeLatitude = 0;
  gpsTracker.homeLongitude = 0;
  gpsTracker.homeAltitude = 0;
  gpsTracker.maxDistance = 0;
  gpsTracker.currentDistance = 0;
  gpsTracker.bearing = 0;
  gpsTracker.satelliteCount = 0;
  gpsTracker.fix3D = false;
  gpsTracker.hdop = 99.99;
}

void updateGPSTracker() {
  if (!gps.location.isValid()) return;

  // Update GPS status
  gpsTracker.satelliteCount = gps.satellites.value();
  gpsTracker.fix3D = (gps.satellites.value() >= 4);
  gpsTracker.hdop = gps.hdop.value() / 100.0;

  // Set home position when armed for first time
  if (armed && !gpsTracker.homeSet && gpsTracker.fix3D) {
    gpsTracker.homeLatitude = gps.location.lat();
    gpsTracker.homeLongitude = gps.location.lng();
    gpsTracker.homeAltitude = sensors.altitude;
    gpsTracker.homeSet = true;

    Serial.println("Home position set:");
    Serial.print("Lat: "); Serial.println(gpsTracker.homeLatitude, 6);
    Serial.print("Lng: "); Serial.println(gpsTracker.homeLongitude, 6);
    Serial.print("Alt: "); Serial.println(gpsTracker.homeAltitude, 1);
  }

  // Calculate distance and bearing to home
  if (gpsTracker.homeSet) {
    gpsTracker.currentDistance = calculateDistance(
      gps.location.lat(), gps.location.lng(),
      gpsTracker.homeLatitude, gpsTracker.homeLongitude
    );

    gpsTracker.bearing = calculateBearing(
      gps.location.lat(), gps.location.lng(),
      gpsTracker.homeLatitude, gpsTracker.homeLongitude
    );

    // Update max distance
    if (gpsTracker.currentDistance > gpsTracker.maxDistance) {
      gpsTracker.maxDistance = gpsTracker.currentDistance;
    }
  }
}

float calculateDistance(float lat1, float lng1, float lat2, float lng2) {
  // Haversine formula for distance calculation
  float dLat = radians(lat2 - lat1);
  float dLng = radians(lng2 - lng1);
  float a = sin(dLat/2) * sin(dLat/2) + cos(radians(lat1)) * cos(radians(lat2)) * sin(dLng/2) * sin(dLng/2);
  float c = 2 * atan2(sqrt(a), sqrt(1-a));
  return 6371000 * c;  // Earth radius in meters
}

float calculateBearing(float lat1, float lng1, float lat2, float lng2) {
  // Calculate bearing from current position to target
  float dLng = radians(lng2 - lng1);
  float lat1Rad = radians(lat1);
  float lat2Rad = radians(lat2);

  float y = sin(dLng) * cos(lat2Rad);
  float x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(dLng);

  float bearing = degrees(atan2(y, x));
  return bearing < 0 ? bearing + 360 : bearing;
}

// ===== ENHANCED DISPLAY MANAGEMENT =====

void updateDisplay() {
  // Check for display mode change (AUX4 switch cycling)
  static bool lastAux4State = false;
  bool currentAux4State = (rcInput.aux4 > 1700);

  if (currentAux4State && !lastAux4State && !armed) {  // Rising edge, only when disarmed
    if (millis() - lastDisplayModeChange > 500) {  // Debounce
      currentDisplayMode = (DisplayMode)((currentDisplayMode + 1) % 5);
      displayNeedsUpdate = true;
      lastDisplayModeChange = millis();

      Serial.print("Display mode changed to: ");
      Serial.println(currentDisplayMode);
    }
  }
  lastAux4State = currentAux4State;

  // Update display based on current mode
  switch (currentDisplayMode) {
    case DISPLAY_BASIC:
      updateBasicDisplay();
      break;
    case DISPLAY_ARTIFICIAL_HORIZON:
      drawArtificialHorizon();
      break;
    case DISPLAY_GPS_MAP:
      drawGPSMap();
      break;
    case DISPLAY_PERFORMANCE:
      drawPerformanceDisplay();
      break;
    case DISPLAY_DIAGNOSTICS:
      drawDiagnosticsDisplay();
      break;
  }

  displayNeedsUpdate = false;
}



// ===== ENHANCED SENSOR CALIBRATION FUNCTIONS =====

void loadSensorCalibration() {
  // Load IMU calibration
  preferences.begin("imu_cal", true);
  if (preferences.getUShort("magic", 0) == EEPROM_MAGIC_NUMBER) {
    imuCal.accelOffsetX = preferences.getFloat("acc_ox", 0);
    imuCal.accelOffsetY = preferences.getFloat("acc_oy", 0);
    imuCal.accelOffsetZ = preferences.getFloat("acc_oz", 0);
    imuCal.accelScaleX = preferences.getFloat("acc_sx", 1.0);
    imuCal.accelScaleY = preferences.getFloat("acc_sy", 1.0);
    imuCal.accelScaleZ = preferences.getFloat("acc_sz", 1.0);
    imuCal.gyroOffsetX = preferences.getFloat("gyr_ox", 0);
    imuCal.gyroOffsetY = preferences.getFloat("gyr_oy", 0);
    imuCal.gyroOffsetZ = preferences.getFloat("gyr_oz", 0);
    imuCal.temperature = preferences.getFloat("temp", 25.0);
    imuCal.valid = true;

    // Update legacy variables
    accelOffsetX = imuCal.accelOffsetX;
    accelOffsetY = imuCal.accelOffsetY;
    accelOffsetZ = imuCal.accelOffsetZ;
    gyroOffsetX = imuCal.gyroOffsetX;
    gyroOffsetY = imuCal.gyroOffsetY;
    gyroOffsetZ = imuCal.gyroOffsetZ;

    Serial.println("IMU calibration loaded");
  } else {
    imuCal.valid = false;
    Serial.println("No IMU calibration found");
  }
  preferences.end();

  // Load Magnetometer calibration
  preferences.begin("mag_cal", true);
  if (preferences.getUShort("magic", 0) == EEPROM_MAGIC_NUMBER) {
    magCal.offsetX = preferences.getFloat("mag_ox", 0);
    magCal.offsetY = preferences.getFloat("mag_oy", 0);
    magCal.offsetZ = preferences.getFloat("mag_oz", 0);
    magCal.scaleX = preferences.getFloat("mag_sx", 1.0);
    magCal.scaleY = preferences.getFloat("mag_sy", 1.0);
    magCal.scaleZ = preferences.getFloat("mag_sz", 1.0);
    magCal.declination = preferences.getFloat("declination", 0);
    magCal.valid = true;

    // Update legacy variables
    magOffsetX = magCal.offsetX;
    magOffsetY = magCal.offsetY;
    magOffsetZ = magCal.offsetZ;

    Serial.println("Magnetometer calibration loaded");
  } else {
    magCal.valid = false;
    Serial.println("No magnetometer calibration found");
  }
  preferences.end();

  // Load Barometer calibration
  preferences.begin("baro_cal", true);
  if (preferences.getUShort("magic", 0) == EEPROM_MAGIC_NUMBER) {
    baroCal.pressureOffset = preferences.getFloat("press_off", 0);
    baroCal.temperatureOffset = preferences.getFloat("temp_off", 0);
    baroCal.seaLevelPressure = preferences.getFloat("sea_press", 101325.0);
    baroCal.valid = true;

    baselinePressure = baroCal.seaLevelPressure;
    Serial.println("Barometer calibration loaded");
  } else {
    baroCal.valid = false;
    Serial.println("No barometer calibration found");
  }
  preferences.end();
}

void saveSensorCalibration() {
  // Save IMU calibration
  preferences.begin("imu_cal", false);
  preferences.putUShort("magic", EEPROM_MAGIC_NUMBER);
  preferences.putFloat("acc_ox", imuCal.accelOffsetX);
  preferences.putFloat("acc_oy", imuCal.accelOffsetY);
  preferences.putFloat("acc_oz", imuCal.accelOffsetZ);
  preferences.putFloat("acc_sx", imuCal.accelScaleX);
  preferences.putFloat("acc_sy", imuCal.accelScaleY);
  preferences.putFloat("acc_sz", imuCal.accelScaleZ);
  preferences.putFloat("gyr_ox", imuCal.gyroOffsetX);
  preferences.putFloat("gyr_oy", imuCal.gyroOffsetY);
  preferences.putFloat("gyr_oz", imuCal.gyroOffsetZ);
  preferences.putFloat("temp", imuCal.temperature);
  preferences.end();

  // Save Magnetometer calibration
  preferences.begin("mag_cal", false);
  preferences.putUShort("magic", EEPROM_MAGIC_NUMBER);
  preferences.putFloat("mag_ox", magCal.offsetX);
  preferences.putFloat("mag_oy", magCal.offsetY);
  preferences.putFloat("mag_oz", magCal.offsetZ);
  preferences.putFloat("mag_sx", magCal.scaleX);
  preferences.putFloat("mag_sy", magCal.scaleY);
  preferences.putFloat("mag_sz", magCal.scaleZ);
  preferences.putFloat("declination", magCal.declination);
  preferences.end();

  // Save Barometer calibration
  preferences.begin("baro_cal", false);
  preferences.putUShort("magic", EEPROM_MAGIC_NUMBER);
  preferences.putFloat("press_off", baroCal.pressureOffset);
  preferences.putFloat("temp_off", baroCal.temperatureOffset);
  preferences.putFloat("sea_press", baroCal.seaLevelPressure);
  preferences.end();

  Serial.println("All sensor calibrations saved");
}

void calibrateBarometer() {
  Serial.println("Calibrating barometer baseline...");

  float pressureSum = 0;
  int samples = 100;

  for (int i = 0; i < samples; i++) {
    int result = barometer.read();
    if (result == MS5611_READ_OK) {
      pressureSum += barometer.getPressure();
    }
    delay(10);
  }

  baselinePressure = pressureSum / samples;
  Serial.print("Baseline pressure set to: ");
  Serial.print(baselinePressure);
  Serial.println(" Pa");
}

// ===== DISPLAY FUNCTIONS =====

void updateDisplay() {
  tft.fillScreen(ST7735_BLACK);
  tft.setCursor(0, 0);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);

  // Flight mode and status
  tft.print("Mode: ");
  switch (currentMode) {
    case MANUAL: tft.println("MANUAL"); break;
    case STABILIZE: tft.println("STAB"); break;
    case ALTITUDE_HOLD: tft.println("ALT HOLD"); break;
    case GPS_HOLD: tft.println("GPS HOLD"); break;
    case RETURN_TO_HOME: tft.println("RTH"); break;
    case ACRO: tft.println("ACRO"); break;
    case CIRCLE: tft.println("CIRCLE"); break;
    case AUTO_LAND: tft.println("AUTO LAND"); break;
    case FAILSAFE: tft.println("FAILSAFE"); break;
  }

  // PID tuning status
  if (tuningActive) {
    tft.setTextColor(ST7735_YELLOW);
    tft.print("TUNING: ");
    switch (tuningMode) {
      case TUNING_ROLL: tft.println("ROLL"); break;
      case TUNING_PITCH: tft.println("PITCH"); break;
      case TUNING_YAW: tft.println("YAW"); break;
      case TUNING_ALTITUDE: tft.println("ALT"); break;
      default: tft.println("OFF"); break;
    }
    tft.setTextColor(ST7735_WHITE);
  }

  tft.print("Armed: ");
  tft.println(armed ? "YES" : "NO");

  // Attitude
  tft.print("Roll: ");
  tft.println(sensors.roll, 1);
  tft.print("Pitch: ");
  tft.println(sensors.pitch, 1);
  tft.print("Yaw: ");
  tft.println(sensors.yaw, 1);

  // Altitude and GPS
  tft.print("Alt: ");
  tft.print(sensors.altitude, 1);
  tft.println("m");

  tft.print("GPS: ");
  if (sensors.gpsValid) {
    tft.print(sensors.satellites);
    tft.println(" sats");
  } else {
    tft.println("NO FIX");
  }

  // Battery
  tft.print("Batt: ");
  tft.print(sensors.batteryVoltage, 1);
  tft.println("V");

  // RC status with signal quality
  tft.print("RC: ");
  if (rcInput.signalLost) {
    tft.setTextColor(ST7735_RED);
    tft.println("LOST");
  } else {
    // Color code signal quality
    if (rcInput.signalQuality >= 90) {
      tft.setTextColor(ST7735_GREEN);
    } else if (rcInput.signalQuality >= 70) {
      tft.setTextColor(ST7735_YELLOW);
    } else {
      tft.setTextColor(ST7735_RED);
    }
    tft.print(rcInput.signalQuality);
    tft.println("%");
  }
  tft.setTextColor(ST7735_WHITE);  // Reset color
}

void displayMessage(const char* message) {
  tft.fillScreen(ST7735_BLACK);
  tft.setCursor(10, 60);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(2);
  tft.println(message);
}

void displayError(const char* error) {
  tft.fillScreen(ST7735_RED);
  tft.setCursor(10, 60);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(2);
  tft.println(error);
}

// ===== UTILITY FUNCTIONS =====

float mapFloat(float x, float in_min, float in_max, float out_min, float out_max) {
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

void printDebugInfo() {
  // Print sensor data for debugging
  Serial.print("Roll: "); Serial.print(sensors.roll, 1);
  Serial.print(" Pitch: "); Serial.print(sensors.pitch, 1);
  Serial.print(" Yaw: "); Serial.print(sensors.yaw, 1);
  Serial.print(" GyroZ: "); Serial.print(sensors.gyroZ, 3);
  Serial.print(" Heading: "); Serial.print(sensors.heading, 1);
  Serial.print(" Alt: "); Serial.print(sensors.altitude, 1);
  Serial.print(" Batt: "); Serial.print(sensors.batteryVoltage, 1);
  Serial.print(" RC: "); Serial.print(rcInput.signalLost ? "LOST" : "OK");
  Serial.print(" Armed: "); Serial.println(armed ? "YES" : "NO");
}

void printYawDebug() {
  // Detailed yaw debugging
  Serial.print("GyroZ: "); Serial.print(sensors.gyroZ, 4);
  Serial.print(" Heading: "); Serial.print(sensors.heading, 2);
  Serial.print(" Yaw: "); Serial.print(sensors.yaw, 2);
  Serial.print(" HMC Available: "); Serial.println(hmc5883l_available ? "YES" : "NO");
}

void printRCDebug() {
  // RC channel debugging
  Serial.print("RC - Thr:"); Serial.print(rcInput.throttle);
  Serial.print(" Roll:"); Serial.print(rcInput.roll);
  Serial.print(" Pitch:"); Serial.print(rcInput.pitch);
  Serial.print(" Yaw:"); Serial.print(rcInput.yaw);
  Serial.print(" AUX3:"); Serial.print(rcInput.aux4);
  Serial.print(" Mode:");
  switch (currentMode) {
    case MANUAL: Serial.print("MAN"); break;
    case STABILIZE: Serial.print("STAB"); break;
    case ALTITUDE_HOLD: Serial.print("ALT"); break;
    case GPS_HOLD: Serial.print("GPS"); break;
    case RETURN_TO_HOME: Serial.print("RTH"); break;
    case FAILSAFE: Serial.print("FAIL"); break;
  }
  Serial.println();
}

// ===== ESC CALIBRATION FUNCTION =====
// Call this function once to calibrate ESCs
// Remove after calibration is complete
void calibrateESCs() {
  Serial.println("ESC Calibration Mode");
  Serial.println("Disconnect battery, press any key when ready...");

  while (!Serial.available()) {
    delay(100);
  }
  Serial.read();

  // Set all ESCs to maximum throttle
  motor1.writeMicroseconds(MOTOR_MAX);
  motor2.writeMicroseconds(MOTOR_MAX);
  motor3.writeMicroseconds(MOTOR_MAX);
  motor4.writeMicroseconds(MOTOR_MAX);

  Serial.println("Connect battery now, wait for beeps, then press any key...");

  while (!Serial.available()) {
    delay(100);
  }
  Serial.read();

  // Set all ESCs to minimum throttle
  motor1.writeMicroseconds(MOTOR_MIN);
  motor2.writeMicroseconds(MOTOR_MIN);
  motor3.writeMicroseconds(MOTOR_MIN);
  motor4.writeMicroseconds(MOTOR_MIN);

  Serial.println("ESC calibration complete!");
  delay(5000);
}

/*
 * USAGE INSTRUCTIONS:
 *
 * 1. HARDWARE SETUP:
 *    - Connect GY-86 module to I2C pins (SDA=21, SCL=22)
 *    - Connect GPS module to pins 16 (RX) and 17 (TX)
 *    - Connect RC receiver PWM outputs to pins 32-39
 *    - Connect ESCs to pins 2, 4, 5, 18
 *    - Connect TFT display via SPI
 *    - Connect battery voltage divider to pin 35
 *
 * 2. FIRST TIME SETUP:
 *    - Uncomment calibrateESCs() call in setup() for ESC calibration
 *    - Upload code and follow ESC calibration procedure
 *    - Comment out calibrateESCs() call and re-upload
 *
 * 3. OPERATION:
 *    - Power on and wait for sensor initialization
 *    - Wait for GPS fix (6+ satellites recommended)
 *    - Arm: Throttle low + Yaw right
 *    - Disarm: Throttle low + Yaw left
 *    - Flight modes via AUX1 channel:
 *      * Low: Manual mode
 *      * Mid: Stabilize mode
 *      * High: Altitude hold mode
 *
 * 4. SAFETY FEATURES:
 *    - Automatic disarm on RC signal loss
 *    - Low battery protection
 *    - Extreme attitude protection
 *    - Emergency landing mode
 *
 * 5. TUNING:
 *    - Adjust PID constants at top of file
 *    - Monitor serial output for debugging
 *    - Use TFT display for real-time status
 *
 * WARNING: This is experimental flight controller code.
 * Always test thoroughly in a safe environment.
 * Remove propellers during initial testing.
 * Follow all local regulations for drone operation.
 */

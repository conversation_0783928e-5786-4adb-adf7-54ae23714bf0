/*
 * Quadcopter Flight Controller
 * ESP32-based flight controller with GY-86 10DOF, GPS, RC receiver, and TFT display
 * 
 * Hardware:
 * - ESP32 DevKit
 * - GY-86 10DOF (MPU6050 + HMC5883L + MS5611)
 * - NEO-6M GPS module
 * - FS-TH9X RC receiver
 * - 1.8" TFT ST7735 display
 * - 4x ESCs for motor control
 * 
 * Author: QuadFly Project
 * Version: 1.0
 */

// ===== LIBRARY INCLUDES =====
#include <Wire.h>
#include <SPI.h>
#include <MPU6050_tockn.h>
#include <Adafruit_HMC5883_U.h>
#include <MS5611.h>
#include <HardwareSerial.h>
#include <TinyGPS++.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>
#include <ESP32Servo.h>

// ===== PIN DEFINITIONS =====
// I2C pins (default for ESP32)
#define SDA_PIN 21
#define SCL_PIN 22

// GPS pins
#define GPS_RX_PIN 16
#define GPS_TX_PIN 17

// RC receiver pins (PWM input)
#define RC_CH1_PIN 14  // Throttle
#define RC_CH2_PIN 13  // Roll
#define RC_CH3_PIN 12  // Pitch
#define RC_CH4_PIN 27  // Yaw
#define RC_CH5_PIN 26  // Flight mode
#define RC_CH6_PIN 25  // Aux 1
#define RC_CH7_PIN 33  // Aux 2
#define RC_CH8_PIN 34  // Aux 3

// Motor ESC pins (PWM output)
#define MOTOR1_PIN 32   // Front Right
#define MOTOR2_PIN 15   // Front Left
#define MOTOR3_PIN 0   // Rear Left
#define MOTOR4_PIN 19  // Rear Right

// TFT display pins
#define TFT_CS_PIN 5
#define TFT_RST_PIN 4
#define TFT_DC_PIN 2
#define TFT_MOSI_PIN 23
#define TFT_SCLK_PIN 18

// Status LED
#define LED_PIN 2

// Battery voltage monitoring
#define BATTERY_PIN 35

// ===== CONSTANTS =====
#define LOOP_FREQUENCY 250  // Hz
#define LOOP_TIME 1000000 / LOOP_FREQUENCY  // microseconds

// RC channel limits
#define RC_MIN 1000
#define RC_MID 1500
#define RC_MAX 2000
#define RC_DEADBAND 50

// Motor limits
#define MOTOR_MIN 1000
#define MOTOR_MAX 2000
#define MOTOR_IDLE 1100

// PID constants
#define PID_ROLL_KP 1.5
#define PID_ROLL_KI 0.05
#define PID_ROLL_KD 0.8

#define PID_PITCH_KP 1.5
#define PID_PITCH_KI 0.05
#define PID_PITCH_KD 0.8

#define PID_YAW_KP 2.0
#define PID_YAW_KI 0.1
#define PID_YAW_KD 0.5

#define PID_ALT_KP 3.0
#define PID_ALT_KI 0.1
#define PID_ALT_KD 1.0

// Flight modes
enum FlightMode {
  MANUAL = 0,
  STABILIZE = 1,
  ALTITUDE_HOLD = 2,
  GPS_HOLD = 3,
  RETURN_TO_HOME = 4,
  FAILSAFE = 5
};

// ===== GLOBAL VARIABLES =====
// Sensor objects
MPU6050 mpu(Wire);
Adafruit_HMC5883_Unified compass = Adafruit_HMC5883_Unified(12345);
MS5611 barometer;
TinyGPSPlus gps;
HardwareSerial gpsSerial(2);  // Use UART2 for GPS
Adafruit_ST7735 tft = Adafruit_ST7735(TFT_CS_PIN, TFT_DC_PIN, TFT_RST_PIN);

// Servo objects for ESCs
Servo motor1, motor2, motor3, motor4;

// Sensor data
struct SensorData {
  // IMU data
  float accelX, accelY, accelZ;
  float gyroX, gyroY, gyroZ;
  float roll, pitch, yaw;
  
  // Magnetometer
  float magX, magY, magZ;
  float heading;
  
  // Barometer
  float pressure;
  float altitude;
  float temperature;
  
  // GPS
  double latitude, longitude;
  float gpsAltitude;
  int satellites;
  bool gpsValid;
  
  // Battery
  float batteryVoltage;
} sensors;

// RC input data
struct RCData {
  int throttle, roll, pitch, yaw;
  int aux1, aux2, aux3, aux4;
  bool signalLost;
  unsigned long lastUpdate;
} rcInput;

// PID controllers
struct PIDController {
  float kp, ki, kd;
  float setpoint, input, output;
  float integral, lastError;
  float outputMin, outputMax;
} pidRoll, pidPitch, pidYaw, pidAlt;

// Motor outputs
int motor1Speed, motor2Speed, motor3Speed, motor4Speed;

// Flight control variables
FlightMode currentMode = STABILIZE;
bool armed = false;
bool calibrationMode = false;
unsigned long lastLoopTime = 0;
unsigned long displayUpdateTime = 0;

// Sensor status flags
bool mpu6050_available = false;
bool hmc5883l_available = false;
bool ms5611_available = false;

// Calibration offsets
float accelOffsetX = 0, accelOffsetY = 0, accelOffsetZ = 0;
float gyroOffsetX = 0, gyroOffsetY = 0, gyroOffsetZ = 0;
float magOffsetX = 0, magOffsetY = 0, magOffsetZ = 0;

// Barometer baseline
float baselinePressure = 101325.0;  // Sea level pressure in Pa

// Fail-safe variables
unsigned long rcLostTime = 0;
bool emergencyLanding = false;
float homeLatitude = 0, homeLongitude = 0;
float homeAltitude = 0;

// ===== SETUP FUNCTION =====
void setup() {
  Serial.begin(115200);
  Serial.println("QuadFly Flight Controller Starting...");
  
  // Initialize pins
  pinMode(LED_PIN, OUTPUT);
  pinMode(BATTERY_PIN, INPUT);
  
  // Initialize I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000);  // 400kHz I2C speed

  // Initialize display
  initDisplay();
  
  // Initialize sensors
  if (!initSensors()) {
    Serial.println("Sensor initialization failed!");
    displayError("SENSOR INIT FAILED");
    while(1) {
      digitalWrite(LED_PIN, !digitalRead(LED_PIN));
      delay(200);
    }
  }
  
  // Initialize RC inputs
  initRCInputs();
  
  // Initialize motors
  initMotors();
  
  // Initialize PID controllers
  initPIDControllers();
  
  // Calibrate sensors
  calibrateSensors();
  
  Serial.println("Initialization complete!");
  displayMessage("READY TO FLY");
  
  // Set home position when GPS gets fix
  setHomePosition();
}

// ===== MAIN LOOP =====
void loop() {
  unsigned long currentTime = micros();
  
  // Maintain loop frequency
  if (currentTime - lastLoopTime >= LOOP_TIME) {
    lastLoopTime = currentTime;
    
    // Read all sensors
    readSensors();
    
    // Read RC inputs
    readRCInputs();
    
    // Check fail-safe conditions
    checkFailSafe();
    
    // Update flight mode
    updateFlightMode();
    
    // Calculate attitude (sensor fusion)
    calculateAttitude();
    
    // Run PID controllers
    runPIDControllers();
    
    // Calculate motor outputs
    calculateMotorOutputs();
    
    // Update motor speeds
    updateMotors();
    
    // Update display (at lower frequency)
    if (currentTime - displayUpdateTime > 100000) {  // 10Hz display update
      updateDisplay();
      displayUpdateTime = currentTime;
    }
    
    // Status LED
    digitalWrite(LED_PIN, armed);
  }
  
  // Process GPS data
  while (gpsSerial.available() > 0) {
    if (gps.encode(gpsSerial.read())) {
      updateGPSData();
    }
  }
}

// ===== INITIALIZATION FUNCTIONS =====

bool initSensors() {
  Serial.println("Initializing sensors...");

  // Scan I2C bus first
  scanI2CDevices();

  // Initialize MPU6050
  Serial.println("Initializing MPU6050...");
  mpu.begin();
  mpu.calcGyroOffsets(true);  // Calculate gyro offsets
  mpu6050_available = true;
  Serial.println("MPU6050 initialized");

  // Initialize HMC5883L with retry logic
  Serial.println("Initializing HMC5883L...");
  delay(100);  // Give sensor time to stabilize

  bool hmc5883l_ok = false;
  for (int attempt = 0; attempt < 3; attempt++) {
    if (compass.begin()) {
      hmc5883l_ok = true;
      break;
    }
    Serial.print("HMC5883L attempt ");
    Serial.print(attempt + 1);
    Serial.println(" failed, retrying...");
    delay(200);
  }

  if (!hmc5883l_ok) {
    Serial.println("HMC5883L initialization failed after 3 attempts!");
    Serial.println("Continuing without magnetometer...");
    hmc5883l_available = false;
  } else {
    Serial.println("HMC5883L initialized");
    hmc5883l_available = true;
  }

  // Initialize MS5611
  Serial.println("Initializing MS5611...");
  if (!barometer.begin()) {
    Serial.println("MS5611 initialization failed!");
    Serial.println("Continuing without barometer...");
    ms5611_available = false;
  } else {
    Serial.println("MS5611 initialized");
    ms5611_available = true;
  }

  // Initialize GPS Serial
  gpsSerial.begin(38400, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);
  Serial.println("GPS Serial initialized");

  Serial.println("All sensors initialized successfully!");
  return true;
}

void scanI2CDevices() {
  Serial.println("Scanning I2C bus...");
  byte error, address;
  int nDevices = 0;

  for(address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    error = Wire.endTransmission();

    if (error == 0) {
      Serial.print("I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);

      // Identify known devices
      switch(address) {
        case 0x68: Serial.println(" (MPU6050)"); break;
        case 0x1E: Serial.println(" (HMC5883L)"); break;
        case 0x77: Serial.println(" (MS5611)"); break;
        default: Serial.println(" (Unknown)"); break;
      }
      nDevices++;
    }
    else if (error == 4) {
      Serial.print("Unknown error at address 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
    }
  }

  if (nDevices == 0) {
    Serial.println("No I2C devices found!");
  } else {
    Serial.print("Found ");
    Serial.print(nDevices);
    Serial.println(" I2C devices");
  }
  Serial.println();
}

void initDisplay() {
  tft.initR(INITR_BLACKTAB);
  tft.setRotation(1);
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);
  tft.println("QuadFly FC v1.0");
  tft.println("Initializing...");
}

void initRCInputs() {
  // Configure RC input pins
  pinMode(RC_CH1_PIN, INPUT);
  pinMode(RC_CH2_PIN, INPUT);
  pinMode(RC_CH3_PIN, INPUT);
  pinMode(RC_CH4_PIN, INPUT);
  pinMode(RC_CH5_PIN, INPUT);
  pinMode(RC_CH6_PIN, INPUT);
  pinMode(RC_CH7_PIN, INPUT);
  pinMode(RC_CH8_PIN, INPUT);

  // Initialize RC data
  rcInput.throttle = RC_MIN;
  rcInput.roll = RC_MID;
  rcInput.pitch = RC_MID;
  rcInput.yaw = RC_MID;
  rcInput.signalLost = true;
  rcInput.lastUpdate = millis();
}

void initMotors() {
  // Attach ESCs to servo pins
  motor1.attach(MOTOR1_PIN, MOTOR_MIN, MOTOR_MAX);
  motor2.attach(MOTOR2_PIN, MOTOR_MIN, MOTOR_MAX);
  motor3.attach(MOTOR3_PIN, MOTOR_MIN, MOTOR_MAX);
  motor4.attach(MOTOR4_PIN, MOTOR_MIN, MOTOR_MAX);

  // Initialize motors to minimum throttle
  motor1.writeMicroseconds(MOTOR_MIN);
  motor2.writeMicroseconds(MOTOR_MIN);
  motor3.writeMicroseconds(MOTOR_MIN);
  motor4.writeMicroseconds(MOTOR_MIN);

  delay(2000);  // ESC initialization delay
}

void initPIDControllers() {
  // Roll PID
  pidRoll.kp = PID_ROLL_KP;
  pidRoll.ki = PID_ROLL_KI;
  pidRoll.kd = PID_ROLL_KD;
  pidRoll.outputMin = -400;
  pidRoll.outputMax = 400;

  // Pitch PID
  pidPitch.kp = PID_PITCH_KP;
  pidPitch.ki = PID_PITCH_KI;
  pidPitch.kd = PID_PITCH_KD;
  pidPitch.outputMin = -400;
  pidPitch.outputMax = 400;

  // Yaw PID
  pidYaw.kp = PID_YAW_KP;
  pidYaw.ki = PID_YAW_KI;
  pidYaw.kd = PID_YAW_KD;
  pidYaw.outputMin = -400;
  pidYaw.outputMax = 400;

  // Altitude PID
  pidAlt.kp = PID_ALT_KP;
  pidAlt.ki = PID_ALT_KI;
  pidAlt.kd = PID_ALT_KD;
  pidAlt.outputMin = -500;
  pidAlt.outputMax = 500;
}

// ===== SENSOR READING FUNCTIONS =====

void readSensors() {
  static unsigned long lastSensorRead = 0;
  static int sensorErrors = 0;

  // Limit sensor reading frequency to reduce I2C errors
  if (millis() - lastSensorRead < 10) return;  // Max 100Hz sensor reading
  lastSensorRead = millis();

  // Read MPU6050 with error handling
  if (mpu6050_available) {
    mpu.update();

    sensors.accelX = mpu.getAccX() - accelOffsetX;
    sensors.accelY = mpu.getAccY() - accelOffsetY;
    sensors.accelZ = mpu.getAccZ() - accelOffsetZ;

    sensors.gyroX = mpu.getGyroX() - gyroOffsetX;
    sensors.gyroY = mpu.getGyroY() - gyroOffsetY;
    sensors.gyroZ = mpu.getGyroZ() - gyroOffsetZ;
  }

  // Read HMC5883L with error handling
  if (hmc5883l_available) {
    sensors_event_t event;
    if (compass.getEvent(&event)) {
      sensors.magX = event.magnetic.x - magOffsetX;
      sensors.magY = event.magnetic.y - magOffsetY;
      sensors.magZ = event.magnetic.z - magOffsetZ;

      // Calculate heading
      sensors.heading = atan2(sensors.magY, sensors.magX) * 180.0 / PI;
      if (sensors.heading < 0) sensors.heading += 360;
    } else {
      sensorErrors++;
      if (sensorErrors % 100 == 0) {  // Print error every 100 failures
        Serial.println("HMC5883L read error");
      }
    }
  } else {
    // Use gyro for heading if magnetometer not available
    static float gyroHeading = 0;
    gyroHeading += sensors.gyroZ * 0.01;  // Integrate gyro Z for heading
    if (gyroHeading < 0) gyroHeading += 360;
    if (gyroHeading >= 360) gyroHeading -= 360;
    sensors.heading = gyroHeading;
  }

  // Read MS5611 with error handling
  int result = barometer.read();
  if (result == MS5611_READ_OK) {
    sensors.pressure = barometer.getPressure();
    sensors.temperature = barometer.getTemperature();

    // Calculate altitude from pressure (standard atmosphere formula)
    // Using baseline pressure for relative altitude
    sensors.altitude = 44330.0 * (1.0 - pow(sensors.pressure / baselinePressure, 0.1903));
  } else {
    sensorErrors++;
    if (sensorErrors % 100 == 0) {  // Print error every 100 failures
      Serial.print("MS5611 read error: ");
      Serial.println(result);
    }
  }

  // Read battery voltage (this doesn't use I2C)
  int batteryRaw = analogRead(BATTERY_PIN);
  sensors.batteryVoltage = (batteryRaw / 4095.0) * 3.3 * 4.0;  // Voltage divider
}

void readRCInputs() {
  unsigned long currentTime = millis();

  // Read PWM signals
  rcInput.throttle = pulseIn(RC_CH1_PIN, HIGH, 25000);
  rcInput.roll = pulseIn(RC_CH2_PIN, HIGH, 25000);
  rcInput.pitch = pulseIn(RC_CH3_PIN, HIGH, 25000);
  rcInput.yaw = pulseIn(RC_CH4_PIN, HIGH, 25000);
  rcInput.aux1 = pulseIn(RC_CH5_PIN, HIGH, 25000);
  rcInput.aux2 = pulseIn(RC_CH6_PIN, HIGH, 25000);
  rcInput.aux3 = pulseIn(RC_CH7_PIN, HIGH, 25000);
  rcInput.aux4 = pulseIn(RC_CH8_PIN, HIGH, 25000);

  // Check for valid signals
  if (rcInput.throttle > 800 && rcInput.throttle < 2200 &&
      rcInput.roll > 800 && rcInput.roll < 2200 &&
      rcInput.pitch > 800 && rcInput.pitch < 2200 &&
      rcInput.yaw > 800 && rcInput.yaw < 2200) {
    rcInput.signalLost = false;
    rcInput.lastUpdate = currentTime;
  } else {
    // Check if signal was lost
    if (currentTime - rcInput.lastUpdate > 1000) {  // 1 second timeout
      rcInput.signalLost = true;
    }
  }
}

void updateGPSData() {
  if (gps.location.isValid()) {
    sensors.latitude = gps.location.lat();
    sensors.longitude = gps.location.lng();
    sensors.gpsValid = true;
  } else {
    sensors.gpsValid = false;
  }

  if (gps.altitude.isValid()) {
    sensors.gpsAltitude = gps.altitude.meters();
  }

  sensors.satellites = gps.satellites.value();
}

// ===== FLIGHT CONTROL FUNCTIONS =====

void calculateAttitude() {
  static float rollAngle = 0, pitchAngle = 0;
  static unsigned long lastTime = 0;
  unsigned long currentTime = micros();
  float dt = (currentTime - lastTime) / 1000000.0;
  lastTime = currentTime;

  // Complementary filter for attitude estimation
  float accelRoll = atan2(sensors.accelY, sensors.accelZ) * 180.0 / PI;
  float accelPitch = atan2(-sensors.accelX, sqrt(sensors.accelY * sensors.accelY + sensors.accelZ * sensors.accelZ)) * 180.0 / PI;

  // Integrate gyroscope data
  rollAngle += sensors.gyroX * dt;
  pitchAngle += sensors.gyroY * dt;

  // Apply complementary filter (98% gyro, 2% accel)
  sensors.roll = 0.98 * rollAngle + 0.02 * accelRoll;
  sensors.pitch = 0.98 * pitchAngle + 0.02 * accelPitch;

  // Update angles for next iteration
  rollAngle = sensors.roll;
  pitchAngle = sensors.pitch;

  // Yaw from magnetometer (simple implementation)
  sensors.yaw = sensors.heading;
}

void runPIDControllers() {
  if (!armed) return;

  static unsigned long lastTime = 0;
  unsigned long currentTime = micros();
  float dt = (currentTime - lastTime) / 1000000.0;
  lastTime = currentTime;

  // Calculate setpoints from RC inputs
  float rollSetpoint = map(rcInput.roll, RC_MIN, RC_MAX, -30, 30);  // ±30 degrees
  float pitchSetpoint = map(rcInput.pitch, RC_MIN, RC_MAX, -30, 30);
  float yawSetpoint = map(rcInput.yaw, RC_MIN, RC_MAX, -180, 180);  // Rate mode

  // Apply deadband
  if (abs(rcInput.roll - RC_MID) < RC_DEADBAND) rollSetpoint = 0;
  if (abs(rcInput.pitch - RC_MID) < RC_DEADBAND) pitchSetpoint = 0;
  if (abs(rcInput.yaw - RC_MID) < RC_DEADBAND) yawSetpoint = 0;

  // Run PID calculations
  pidRoll.output = calculatePID(&pidRoll, rollSetpoint, sensors.roll, dt);
  pidPitch.output = calculatePID(&pidPitch, pitchSetpoint, sensors.pitch, dt);
  pidYaw.output = calculatePID(&pidYaw, yawSetpoint, sensors.gyroZ, dt);  // Rate mode

  // Altitude hold (if enabled)
  if (currentMode == ALTITUDE_HOLD || currentMode == GPS_HOLD) {
    static float altitudeSetpoint = sensors.altitude;
    if (abs(rcInput.throttle - RC_MID) > RC_DEADBAND) {
      altitudeSetpoint = sensors.altitude;  // Update setpoint when throttle moved
    }
    pidAlt.output = calculatePID(&pidAlt, altitudeSetpoint, sensors.altitude, dt);
  }
}

float calculatePID(PIDController* pid, float setpoint, float input, float dt) {
  float error = setpoint - input;

  // Proportional term
  float pTerm = pid->kp * error;

  // Integral term
  pid->integral += error * dt;
  pid->integral = constrain(pid->integral, -100, 100);  // Prevent windup
  float iTerm = pid->ki * pid->integral;

  // Derivative term
  float dTerm = pid->kd * (error - pid->lastError) / dt;
  pid->lastError = error;

  // Calculate output
  float output = pTerm + iTerm + dTerm;
  return constrain(output, pid->outputMin, pid->outputMax);
}

void calculateMotorOutputs() {
  if (!armed) {
    motor1Speed = MOTOR_MIN;
    motor2Speed = MOTOR_MIN;
    motor3Speed = MOTOR_MIN;
    motor4Speed = MOTOR_MIN;
    return;
  }

  // Base throttle from RC input
  int baseThrottle = map(rcInput.throttle, RC_MIN, RC_MAX, MOTOR_IDLE, MOTOR_MAX);

  // Add altitude hold correction if enabled
  if (currentMode == ALTITUDE_HOLD || currentMode == GPS_HOLD) {
    baseThrottle += pidAlt.output;
  }

  // Mix PID outputs with throttle
  // Motor layout: 1=FR, 2=FL, 3=RL, 4=RR
  motor1Speed = baseThrottle - pidRoll.output + pidPitch.output - pidYaw.output;  // Front Right
  motor2Speed = baseThrottle + pidRoll.output + pidPitch.output + pidYaw.output;  // Front Left
  motor3Speed = baseThrottle + pidRoll.output - pidPitch.output - pidYaw.output;  // Rear Left
  motor4Speed = baseThrottle - pidRoll.output - pidPitch.output + pidYaw.output;  // Rear Right

  // Constrain motor speeds
  motor1Speed = constrain(motor1Speed, MOTOR_MIN, MOTOR_MAX);
  motor2Speed = constrain(motor2Speed, MOTOR_MIN, MOTOR_MAX);
  motor3Speed = constrain(motor3Speed, MOTOR_MIN, MOTOR_MAX);
  motor4Speed = constrain(motor4Speed, MOTOR_MIN, MOTOR_MAX);
}

void updateMotors() {
  motor1.writeMicroseconds(motor1Speed);
  motor2.writeMicroseconds(motor2Speed);
  motor3.writeMicroseconds(motor3Speed);
  motor4.writeMicroseconds(motor4Speed);
}

// ===== FLIGHT MODE AND SAFETY FUNCTIONS =====

void updateFlightMode() {
  // Check arming conditions
  if (!armed && rcInput.throttle < RC_MIN + 50 &&
      rcInput.yaw > RC_MAX - 50 && !rcInput.signalLost) {
    // Arm sequence: throttle low, yaw right
    armed = true;
    Serial.println("ARMED");
  } else if (armed && rcInput.throttle < RC_MIN + 50 &&
             rcInput.yaw < RC_MIN + 50) {
    // Disarm sequence: throttle low, yaw left
    armed = false;
    Serial.println("DISARMED");
  }

  // Flight mode switching based on aux channel
  if (rcInput.aux1 < 1300) {
    currentMode = MANUAL;
  } else if (rcInput.aux1 < 1700) {
    currentMode = STABILIZE;
  } else {
    currentMode = ALTITUDE_HOLD;
  }

  // Override with failsafe if needed
  if (emergencyLanding) {
    currentMode = FAILSAFE;
  }
}

void checkFailSafe() {
  // RC signal loss
  if (rcInput.signalLost && armed) {
    if (rcLostTime == 0) {
      rcLostTime = millis();
    } else if (millis() - rcLostTime > 3000) {  // 3 second timeout
      emergencyLanding = true;
      Serial.println("FAILSAFE: RC SIGNAL LOST");
    }
  } else {
    rcLostTime = 0;
  }

  // Low battery
  if (sensors.batteryVoltage < 10.5 && armed) {  // 3S LiPo low voltage
    emergencyLanding = true;
    Serial.println("FAILSAFE: LOW BATTERY");
  }

  // Extreme attitude
  if ((abs(sensors.roll) > 60 || abs(sensors.pitch) > 60) && armed) {
    emergencyLanding = true;
    Serial.println("FAILSAFE: EXTREME ATTITUDE");
  }

  // Emergency landing procedure
  if (emergencyLanding && armed) {
    // Gradually reduce throttle
    static int emergencyThrottle = rcInput.throttle;
    emergencyThrottle -= 2;  // Reduce by 2 per loop cycle
    if (emergencyThrottle < MOTOR_IDLE) emergencyThrottle = MOTOR_IDLE;

    // Override motor outputs for controlled descent
    motor1Speed = emergencyThrottle;
    motor2Speed = emergencyThrottle;
    motor3Speed = emergencyThrottle;
    motor4Speed = emergencyThrottle;
  }
}

void setHomePosition() {
  static bool homeSet = false;
  if (!homeSet && sensors.gpsValid && sensors.satellites >= 6) {
    homeLatitude = sensors.latitude;
    homeLongitude = sensors.longitude;
    homeAltitude = sensors.altitude;
    homeSet = true;
    Serial.println("Home position set");
  }
}

// ===== CALIBRATION FUNCTIONS =====

void calibrateSensors() {
  Serial.println("Starting sensor calibration...");
  displayMessage("CALIBRATING...");

  // Calibrate accelerometer and gyroscope
  calibrateIMU();

  // Calibrate magnetometer
  calibrateMagnetometer();

  // Set baseline pressure for altitude reference
  calibrateBarometer();

  Serial.println("Calibration complete!");
  displayMessage("CALIBRATION DONE");
  delay(2000);
}

void calibrateIMU() {
  Serial.println("Calibrating IMU... Keep level and still");

  float accelSumX = 0, accelSumY = 0, accelSumZ = 0;
  float gyroSumX = 0, gyroSumY = 0, gyroSumZ = 0;
  int samples = 1000;

  for (int i = 0; i < samples; i++) {
    mpu.update();

    accelSumX += mpu.getAccX();
    accelSumY += mpu.getAccY();
    accelSumZ += mpu.getAccZ();

    gyroSumX += mpu.getGyroX();
    gyroSumY += mpu.getGyroY();
    gyroSumZ += mpu.getGyroZ();

    delay(5);
  }

  accelOffsetX = accelSumX / samples;
  accelOffsetY = accelSumY / samples;
  accelOffsetZ = (accelSumZ / samples) - 1.0;  // Remove gravity (1g)

  gyroOffsetX = gyroSumX / samples;
  gyroOffsetY = gyroSumY / samples;
  gyroOffsetZ = gyroSumZ / samples;

  Serial.println("IMU calibration complete");
}

void calibrateMagnetometer() {
  Serial.println("Calibrating magnetometer... Rotate in all directions");

  float magMinX = 1000, magMaxX = -1000;
  float magMinY = 1000, magMaxY = -1000;
  float magMinZ = 1000, magMaxZ = -1000;

  unsigned long startTime = millis();
  while (millis() - startTime < 30000) {  // 30 second calibration
    sensors_event_t event;
    compass.getEvent(&event);

    if (event.magnetic.x < magMinX) magMinX = event.magnetic.x;
    if (event.magnetic.x > magMaxX) magMaxX = event.magnetic.x;
    if (event.magnetic.y < magMinY) magMinY = event.magnetic.y;
    if (event.magnetic.y > magMaxY) magMaxY = event.magnetic.y;
    if (event.magnetic.z < magMinZ) magMinZ = event.magnetic.z;
    if (event.magnetic.z > magMaxZ) magMaxZ = event.magnetic.z;

    delay(50);
  }

  magOffsetX = (magMaxX + magMinX) / 2;
  magOffsetY = (magMaxY + magMinY) / 2;
  magOffsetZ = (magMaxZ + magMinZ) / 2;

  Serial.println("Magnetometer calibration complete");
}

void calibrateBarometer() {
  Serial.println("Calibrating barometer baseline...");

  float pressureSum = 0;
  int samples = 100;

  for (int i = 0; i < samples; i++) {
    int result = barometer.read();
    if (result == MS5611_READ_OK) {
      pressureSum += barometer.getPressure();
    }
    delay(10);
  }

  baselinePressure = pressureSum / samples;
  Serial.print("Baseline pressure set to: ");
  Serial.print(baselinePressure);
  Serial.println(" Pa");
}

// ===== DISPLAY FUNCTIONS =====

void updateDisplay() {
  tft.fillScreen(ST7735_BLACK);
  tft.setCursor(0, 0);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);

  // Flight mode and status
  tft.print("Mode: ");
  switch (currentMode) {
    case MANUAL: tft.println("MANUAL"); break;
    case STABILIZE: tft.println("STAB"); break;
    case ALTITUDE_HOLD: tft.println("ALT HOLD"); break;
    case GPS_HOLD: tft.println("GPS HOLD"); break;
    case RETURN_TO_HOME: tft.println("RTH"); break;
    case FAILSAFE: tft.println("FAILSAFE"); break;
  }

  tft.print("Armed: ");
  tft.println(armed ? "YES" : "NO");

  // Attitude
  tft.print("Roll: ");
  tft.println(sensors.roll, 1);
  tft.print("Pitch: ");
  tft.println(sensors.pitch, 1);
  tft.print("Yaw: ");
  tft.println(sensors.yaw, 1);

  // Altitude and GPS
  tft.print("Alt: ");
  tft.print(sensors.altitude, 1);
  tft.println("m");

  tft.print("GPS: ");
  if (sensors.gpsValid) {
    tft.print(sensors.satellites);
    tft.println(" sats");
  } else {
    tft.println("NO FIX");
  }

  // Battery
  tft.print("Batt: ");
  tft.print(sensors.batteryVoltage, 1);
  tft.println("V");

  // RC status
  tft.print("RC: ");
  tft.println(rcInput.signalLost ? "LOST" : "OK");
}

void displayMessage(const char* message) {
  tft.fillScreen(ST7735_BLACK);
  tft.setCursor(10, 60);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(2);
  tft.println(message);
}

void displayError(const char* error) {
  tft.fillScreen(ST7735_RED);
  tft.setCursor(10, 60);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(2);
  tft.println(error);
}

// ===== UTILITY FUNCTIONS =====

float mapFloat(float x, float in_min, float in_max, float out_min, float out_max) {
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

void printDebugInfo() {
  // Print sensor data for debugging
  Serial.print("Roll: "); Serial.print(sensors.roll);
  Serial.print(" Pitch: "); Serial.print(sensors.pitch);
  Serial.print(" Yaw: "); Serial.print(sensors.yaw);
  Serial.print(" Alt: "); Serial.print(sensors.altitude);
  Serial.print(" Batt: "); Serial.print(sensors.batteryVoltage);
  Serial.print(" RC: "); Serial.print(rcInput.signalLost ? "LOST" : "OK");
  Serial.print(" Armed: "); Serial.println(armed ? "YES" : "NO");
}

// ===== ESC CALIBRATION FUNCTION =====
// Call this function once to calibrate ESCs
// Remove after calibration is complete
void calibrateESCs() {
  Serial.println("ESC Calibration Mode");
  Serial.println("Disconnect battery, press any key when ready...");

  while (!Serial.available()) {
    delay(100);
  }
  Serial.read();

  // Set all ESCs to maximum throttle
  motor1.writeMicroseconds(MOTOR_MAX);
  motor2.writeMicroseconds(MOTOR_MAX);
  motor3.writeMicroseconds(MOTOR_MAX);
  motor4.writeMicroseconds(MOTOR_MAX);

  Serial.println("Connect battery now, wait for beeps, then press any key...");

  while (!Serial.available()) {
    delay(100);
  }
  Serial.read();

  // Set all ESCs to minimum throttle
  motor1.writeMicroseconds(MOTOR_MIN);
  motor2.writeMicroseconds(MOTOR_MIN);
  motor3.writeMicroseconds(MOTOR_MIN);
  motor4.writeMicroseconds(MOTOR_MIN);

  Serial.println("ESC calibration complete!");
  delay(5000);
}

/*
 * USAGE INSTRUCTIONS:
 *
 * 1. HARDWARE SETUP:
 *    - Connect GY-86 module to I2C pins (SDA=21, SCL=22)
 *    - Connect GPS module to pins 16 (RX) and 17 (TX)
 *    - Connect RC receiver PWM outputs to pins 32-39
 *    - Connect ESCs to pins 2, 4, 5, 18
 *    - Connect TFT display via SPI
 *    - Connect battery voltage divider to pin 35
 *
 * 2. FIRST TIME SETUP:
 *    - Uncomment calibrateESCs() call in setup() for ESC calibration
 *    - Upload code and follow ESC calibration procedure
 *    - Comment out calibrateESCs() call and re-upload
 *
 * 3. OPERATION:
 *    - Power on and wait for sensor initialization
 *    - Wait for GPS fix (6+ satellites recommended)
 *    - Arm: Throttle low + Yaw right
 *    - Disarm: Throttle low + Yaw left
 *    - Flight modes via AUX1 channel:
 *      * Low: Manual mode
 *      * Mid: Stabilize mode
 *      * High: Altitude hold mode
 *
 * 4. SAFETY FEATURES:
 *    - Automatic disarm on RC signal loss
 *    - Low battery protection
 *    - Extreme attitude protection
 *    - Emergency landing mode
 *
 * 5. TUNING:
 *    - Adjust PID constants at top of file
 *    - Monitor serial output for debugging
 *    - Use TFT display for real-time status
 *
 * WARNING: This is experimental flight controller code.
 * Always test thoroughly in a safe environment.
 * Remove propellers during initial testing.
 * Follow all local regulations for drone operation.
 */

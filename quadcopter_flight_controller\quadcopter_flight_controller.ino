/*
 * Quadcopter Flight Controller
 * ESP32-based flight controller with GY-86 10DOF, GPS, RC receiver, and TFT display
 * 
 * Hardware:
 * - ESP32 DevKit
 * - GY-86 10DOF (MPU6050 + HMC5883L + MS5611)
 * - NEO-6M GPS module
 * - FS-TH9X RC receiver
 * - 1.8" TFT ST7735 display
 * - 4x ESCs for motor control
 * 
 * Author: QuadFly Project
 * Version: 1.2 - Enhanced calibration and RC validation
 */

// ===== LIBRARY INCLUDES =====
#include <Wire.h>
#include <SPI.h>
#include <MPU6050_tockn.h>
#include <HMC5883L.h>
#include <MS5611.h>
#include <HardwareSerial.h>
#include <TinyGPS++.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>
#include <ESP32Servo.h>
#include <EEPROM.h>
#include <Preferences.h>

// ===== PIN DEFINITIONS =====
// I2C pins (default for ESP32)
#define SDA_PIN 21
#define SCL_PIN 22

// GPS pins
#define GPS_RX_PIN 16
#define GPS_TX_PIN 17

// RC receiver pins (PWM input)
#define RC_CH1_PIN 14  // Throttle
#define RC_CH2_PIN 13  // Roll
#define RC_CH3_PIN 12  // Pitch
#define RC_CH4_PIN 27  // Yaw
#define RC_CH5_PIN 26  // Flight mode
#define RC_CH6_PIN 25  // Aux 1
#define RC_CH7_PIN 33  // Aux 2
#define RC_CH8_PIN 34  // Aux 3

// Motor ESC pins (PWM output)
#define MOTOR1_PIN 32   // Front Right
#define MOTOR2_PIN 15   // Front Left
#define MOTOR3_PIN 0   // Rear Left
#define MOTOR4_PIN 19  // Rear Right

// TFT display pins
#define TFT_CS_PIN 5
#define TFT_RST_PIN 4
#define TFT_DC_PIN 2
#define TFT_MOSI_PIN 23
#define TFT_SCLK_PIN 18

// Status LED
#define LED_PIN 2

// Battery voltage monitoring
#define BATTERY_PIN 35

// ===== EEPROM ADDRESSES =====
#define EEPROM_SIZE 512
#define EEPROM_RC_CAL_ADDR 0
#define EEPROM_IMU_CAL_ADDR 50
#define EEPROM_MAG_CAL_ADDR 100
#define EEPROM_BARO_CAL_ADDR 150
#define EEPROM_MAGIC_NUMBER 0xABCD  // To verify valid calibration data

// ===== CONSTANTS =====
#define LOOP_FREQUENCY 250  // Hz
#define LOOP_TIME 1000000 / LOOP_FREQUENCY  // microseconds

// RC channel limits
#define RC_MIN 1000
#define RC_MID 1500
#define RC_MAX 2000
#define RC_DEADBAND 50

// Motor limits
#define MOTOR_MIN 1000
#define MOTOR_MAX 2000
#define MOTOR_IDLE 1100

// PID constants
#define PID_ROLL_KP 1.5
#define PID_ROLL_KI 0.05
#define PID_ROLL_KD 0.8

#define PID_PITCH_KP 1.5
#define PID_PITCH_KI 0.05
#define PID_PITCH_KD 0.8

#define PID_YAW_KP 2.0
#define PID_YAW_KI 0.1
#define PID_YAW_KD 0.5

#define PID_ALT_KP 3.0
#define PID_ALT_KI 0.1
#define PID_ALT_KD 1.0

// Flight modes
enum FlightMode {
  MANUAL = 0,
  STABILIZE = 1,
  ALTITUDE_HOLD = 2,
  GPS_HOLD = 3,
  RETURN_TO_HOME = 4,
  FAILSAFE = 5
};

// ===== GLOBAL VARIABLES =====
// Sensor objects
MPU6050 mpu(Wire);
HMC5883L compass = HMC5883L();
MS5611 barometer;  // MS5611 barometric pressure sensor
TinyGPSPlus gps;
HardwareSerial gpsSerial(2);  // Use UART2 for GPS
Adafruit_ST7735 tft = Adafruit_ST7735(TFT_CS_PIN, TFT_DC_PIN, TFT_RST_PIN);

// Storage for calibration data
Preferences preferences;

// Servo objects for ESCs
Servo motor1, motor2, motor3, motor4;

// Sensor data
struct SensorData {
  // IMU data
  float accelX, accelY, accelZ;
  float gyroX, gyroY, gyroZ;
  float roll, pitch, yaw;
  
  // Magnetometer
  float magX, magY, magZ;
  float heading;
  
  // Barometer
  float pressure;
  float altitude;
  float temperature;
  
  // GPS
  double latitude, longitude;
  float gpsAltitude;
  int satellites;
  bool gpsValid;
  
  // Battery
  float batteryVoltage;
} sensors;

// RC input data
struct RCData {
  int throttle, roll, pitch, yaw;
  int aux1, aux2, aux3, aux4;
  bool signalLost;
  unsigned long lastUpdate;

  // RC Signal Quality
  int signalQuality;        // 0-100%
  int failedReads;          // Count of failed reads
  int totalReads;           // Total read attempts
  unsigned long lastGoodSignal; // Last time we had good signal

  // RC Calibration
  int throttleMin, throttleMax;
  int rollMin, rollMax, rollCenter;
  int pitchMin, pitchMax, pitchCenter;
  int yawMin, yawMax, yawCenter;
  bool calibrationMode;
  bool calibrationComplete;
} rcInput;

// PID controllers
struct PIDController {
  float kp, ki, kd;
  float setpoint, input, output;
  float integral, lastError;
  float outputMin, outputMax;
} pidRoll, pidPitch, pidYaw, pidAlt;

// Motor outputs
int motor1Speed, motor2Speed, motor3Speed, motor4Speed;

// Flight control variables
FlightMode currentMode = STABILIZE;
bool armed = false;
bool calibrationMode = false;
unsigned long lastLoopTime = 0;
unsigned long displayUpdateTime = 0;

// Sensor status flags
bool mpu6050_available = false;
bool hmc5883l_available = false;
bool ms5611_available = false;

// Enhanced calibration data structures
struct IMUCalibration {
  float accelOffsetX, accelOffsetY, accelOffsetZ;
  float accelScaleX, accelScaleY, accelScaleZ;
  float gyroOffsetX, gyroOffsetY, gyroOffsetZ;
  float gyroDriftX, gyroDriftY, gyroDriftZ;  // Temperature compensation
  float temperature;  // Reference temperature
  bool valid;
} imuCal;

struct MagCalibration {
  float offsetX, offsetY, offsetZ;  // Hard iron correction
  float scaleX, scaleY, scaleZ;     // Soft iron correction
  float declination;                // Magnetic declination
  bool valid;
} magCal;

struct BaroCalibration {
  float pressureOffset;
  float temperatureOffset;
  float seaLevelPressure;
  bool valid;
} baroCal;

// Legacy variables for compatibility
float accelOffsetX = 0, accelOffsetY = 0, accelOffsetZ = 0;
float gyroOffsetX = 0, gyroOffsetY = 0, gyroOffsetZ = 0;
float magOffsetX = 0, magOffsetY = 0, magOffsetZ = 0;

// Barometer baseline
float baselinePressure = 101325.0;  // Sea level pressure in Pa

// Fail-safe variables
unsigned long rcLostTime = 0;
bool emergencyLanding = false;
float homeLatitude = 0, homeLongitude = 0;
float homeAltitude = 0;

// ===== SETUP FUNCTION =====
void setup() {
  Serial.begin(115200);
  Serial.println("QuadFly Flight Controller Starting...");
  
  // Initialize pins
  pinMode(LED_PIN, OUTPUT);
  pinMode(BATTERY_PIN, INPUT);
  
  // Initialize I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000);  // 400kHz I2C speed

  // Initialize display
  initDisplay();
  
  // Initialize sensors
  if (!initSensors()) {
    Serial.println("Sensor initialization failed!");
    displayError("SENSOR INIT FAILED");
    while(1) {
      digitalWrite(LED_PIN, !digitalRead(LED_PIN));
      delay(200);
    }
  }
  
  // Initialize RC inputs
  initRCInputs();
  
  // Initialize motors
  initMotors();
  
  // Initialize PID controllers
  initPIDControllers();

  // Load saved calibration data
  loadSensorCalibration();

  // Calibrate sensors (will use saved data if available)
  calibrateSensors();
  
  Serial.println("Initialization complete!");
  displayMessage("READY TO FLY");
  
  // Set home position when GPS gets fix
  setHomePosition();
}

// ===== MAIN LOOP =====
void loop() {
  unsigned long currentTime = micros();
  
  // Maintain loop frequency
  if (currentTime - lastLoopTime >= LOOP_TIME) {
    lastLoopTime = currentTime;
    
    // Read all sensors
    readSensors();
    
    // Read RC inputs
    readRCInputs();

    // Update RC calibration if in progress
    updateRCCalibration();

    // Check for RC calibration trigger (AUX2 switch high when disarmed)
    if (!armed && rcInput.aux2 > 1700 && !rcInput.calibrationMode) {
      startRCCalibration();
    }

    // Check for full sensor recalibration trigger (AUX1 + AUX2 both high when disarmed)
    if (!armed && rcInput.aux1 > 1700 && rcInput.aux2 > 1700 && !rcInput.calibrationMode) {
      forceFullCalibration();
    }

    // Check fail-safe conditions
    checkFailSafe();
    
    // Update flight mode
    updateFlightMode();
    
    // Calculate attitude (sensor fusion)
    calculateAttitude();
    
    // Run PID controllers
    runPIDControllers();
    
    // Calculate motor outputs
    calculateMotorOutputs();
    
    // Update motor speeds
    updateMotors();
    
    // Update display (at lower frequency)
    if (currentTime - displayUpdateTime > 100000) {  // 10Hz display update
      updateDisplay();
      displayUpdateTime = currentTime;
    }

    // Debug output (uncomment for RC/flight mode debugging)
    static unsigned long lastDebugTime = 0;
    if (currentTime - lastDebugTime > 1000000) {  // 1Hz debug output
      printRCDebug();  // Shows RC values and flight mode
      // printYawDebug();  // Uncomment for yaw debugging
      lastDebugTime = currentTime;
    }

    // Status LED
    digitalWrite(LED_PIN, armed);
  }
  
  // Process GPS data
  while (gpsSerial.available() > 0) {
    if (gps.encode(gpsSerial.read())) {
      updateGPSData();
    }
  }
}

// ===== INITIALIZATION FUNCTIONS =====

bool initSensors() {
  Serial.println("Initializing sensors...");

  // Scan I2C bus first
  scanI2CDevices();

  // Initialize MPU6050
  Serial.println("Initializing MPU6050...");
  mpu.begin();
  mpu.calcGyroOffsets(true);  // Calculate gyro offsets
  mpu6050_available = true;
  Serial.println("MPU6050 initialized");

  // Initialize HMC5883L with improved initialization
  Serial.println("Initializing HMC5883L...");
  hmc5883l_available = false;
  
  // Try different I2C addresses for HMC5883L
  uint8_t hmc_addresses[] = {0x1E, 0x0D, 0x3C, 0x3D};
  
  for (int addr_idx = 0; addr_idx < 4; addr_idx++) {
    Serial.print("Trying HMC5883L at address 0x");
    Serial.println(hmc_addresses[addr_idx], HEX);
    
    // Manual I2C test first
    Wire.beginTransmission(hmc_addresses[addr_idx]);
    uint8_t error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.println("I2C device responds at this address");
      
      // Try to initialize the compass with retries
      for (int attempt = 0; attempt < 5; attempt++) {
        delay(100);  // Give more time between attempts
        
        // Reset I2C bus
        Wire.begin(SDA_PIN, SCL_PIN);
        Wire.setClock(100000);  // Slower I2C speed for HMC5883L
        delay(50);
        
        if (compass.begin()) {
          Serial.println("HMC5883L initialized successfully!");

          // Configure the sensor
          compass.setRange(HMC5883L_RANGE_1_3GA);
          compass.setMeasurementMode(HMC5883L_CONTINOUS);
          compass.setDataRate(HMC5883L_DATARATE_15HZ);
          compass.setSamples(HMC5883L_SAMPLES_1);

          // Test reading from the sensor
          delay(100);
          Vector mag = compass.readNormalize();
          if (mag.XAxis != 0 || mag.YAxis != 0 || mag.ZAxis != 0) {
            Serial.print("Test reading - X:");
            Serial.print(mag.XAxis);
            Serial.print(" Y:");
            Serial.print(mag.YAxis);
            Serial.print(" Z:");
            Serial.println(mag.ZAxis);
            hmc5883l_available = true;
            break;
          } else {
            Serial.println("HMC5883L initialized but test read failed");
            hmc5883l_available = false;
          }
        } else {
          Serial.print("HMC5883L attempt ");
          Serial.print(attempt + 1);
          Serial.println(" failed, retrying...");
        }
        delay(200);
      }
      
      if (hmc5883l_available) break;
    } else {
      Serial.print("No response at address 0x");
      Serial.println(hmc_addresses[addr_idx], HEX);
    }
  }

  if (!hmc5883l_available) {
    Serial.println("HMC5883L initialization failed at all addresses!");
    Serial.println("Continuing without magnetometer...");
  }

  // Reset I2C speed back to normal after HMC5883L init
  Wire.setClock(400000);

  // Initialize MS5611
  Serial.println("Initializing MS5611...");
  if (!barometer.begin()) {
    Serial.println("MS5611 initialization failed!");
    Serial.println("Continuing without barometer...");
    ms5611_available = false;
  } else {
    Serial.println("MS5611 initialized");
    ms5611_available = true;
  }

  // Initialize GPS Serial
  gpsSerial.begin(38400, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);
  Serial.println("GPS Serial initialized");

  Serial.println("Sensor initialization complete!");
  return true;
}

// Also add this improved I2C scanner function
void scanI2CDevices() {
  Serial.println("Scanning I2C bus...");
  byte error, address;
  int nDevices = 0;

  // Reset I2C bus
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000);  // Use slower speed for scanning
  delay(100);

  for(address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    error = Wire.endTransmission();

    if (error == 0) {
      Serial.print("I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);

      // Identify known devices
      switch(address) {
        case 0x68: Serial.println(" (MPU6050)"); break;
        case 0x1E: Serial.println(" (HMC5883L)"); break;
        case 0x0D: Serial.println(" (HMC5883L - alternate)"); break;
        case 0x3C: Serial.println(" (HMC5883L - alternate)"); break;
        case 0x3D: Serial.println(" (HMC5883L - alternate)"); break;
        case 0x77: Serial.println(" (MS5611)"); break;
        default: Serial.println(" (Unknown)"); break;
      }
      nDevices++;
    }
    else if (error == 4) {
      Serial.print("Unknown error at address 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
    }
  }

  if (nDevices == 0) {
    Serial.println("No I2C devices found!");
  } else {
    Serial.print("Found ");
    Serial.print(nDevices);
    Serial.println(" I2C devices");
  }
  
  // Reset to normal I2C speed
  Wire.setClock(400000);
  Serial.println();
}

void initDisplay() {
  tft.initR(INITR_BLACKTAB);
  tft.setRotation(1);
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);
  tft.println("QuadFly FC v1.0");
  tft.println("Initializing...");
}

void initRCInputs() {
  // Configure RC input pins
  pinMode(RC_CH1_PIN, INPUT);
  pinMode(RC_CH2_PIN, INPUT);
  pinMode(RC_CH3_PIN, INPUT);
  pinMode(RC_CH4_PIN, INPUT);
  pinMode(RC_CH5_PIN, INPUT);
  pinMode(RC_CH6_PIN, INPUT);
  pinMode(RC_CH7_PIN, INPUT);
  pinMode(RC_CH8_PIN, INPUT);

  // Initialize RC data
  rcInput.throttle = RC_MIN;
  rcInput.roll = RC_MID;
  rcInput.pitch = RC_MID;
  rcInput.yaw = RC_MID;
  rcInput.signalLost = true;
  rcInput.lastUpdate = millis();

  // Initialize RC signal quality
  rcInput.signalQuality = 0;
  rcInput.failedReads = 0;
  rcInput.totalReads = 0;
  rcInput.lastGoodSignal = millis();

  // Initialize RC calibration
  rcInput.calibrationMode = false;
  rcInput.calibrationComplete = false;

  // Load RC calibration from storage
  loadRCCalibration();
}

void initMotors() {
  // Attach ESCs to servo pins
  motor1.attach(MOTOR1_PIN, MOTOR_MIN, MOTOR_MAX);
  motor2.attach(MOTOR2_PIN, MOTOR_MIN, MOTOR_MAX);
  motor3.attach(MOTOR3_PIN, MOTOR_MIN, MOTOR_MAX);
  motor4.attach(MOTOR4_PIN, MOTOR_MIN, MOTOR_MAX);

  // Initialize motors to minimum throttle
  motor1.writeMicroseconds(MOTOR_MIN);
  motor2.writeMicroseconds(MOTOR_MIN);
  motor3.writeMicroseconds(MOTOR_MIN);
  motor4.writeMicroseconds(MOTOR_MIN);

  delay(2000);  // ESC initialization delay
}

void initPIDControllers() {
  // Roll PID
  pidRoll.kp = PID_ROLL_KP;
  pidRoll.ki = PID_ROLL_KI;
  pidRoll.kd = PID_ROLL_KD;
  pidRoll.outputMin = -400;
  pidRoll.outputMax = 400;

  // Pitch PID
  pidPitch.kp = PID_PITCH_KP;
  pidPitch.ki = PID_PITCH_KI;
  pidPitch.kd = PID_PITCH_KD;
  pidPitch.outputMin = -400;
  pidPitch.outputMax = 400;

  // Yaw PID
  pidYaw.kp = PID_YAW_KP;
  pidYaw.ki = PID_YAW_KI;
  pidYaw.kd = PID_YAW_KD;
  pidYaw.outputMin = -400;
  pidYaw.outputMax = 400;

  // Altitude PID
  pidAlt.kp = PID_ALT_KP;
  pidAlt.ki = PID_ALT_KI;
  pidAlt.kd = PID_ALT_KD;
  pidAlt.outputMin = -500;
  pidAlt.outputMax = 500;
}

// ===== SENSOR READING FUNCTIONS =====

void readSensors() {
  static unsigned long lastSensorRead = 0;
  static int sensorErrors = 0;
  static int consecutiveHMCErrors = 0;

  // Limit sensor reading frequency to reduce I2C errors
  if (millis() - lastSensorRead < 10) return;  // Max 100Hz sensor reading
  lastSensorRead = millis();

  // Read MPU6050 with error handling
  if (mpu6050_available) {
    mpu.update();

    sensors.accelX = mpu.getAccX() - accelOffsetX;
    sensors.accelY = mpu.getAccY() - accelOffsetY;
    sensors.accelZ = mpu.getAccZ() - accelOffsetZ;

    sensors.gyroX = mpu.getGyroX() - gyroOffsetX;
    sensors.gyroY = mpu.getGyroY() - gyroOffsetY;
    sensors.gyroZ = mpu.getGyroZ() - gyroOffsetZ;
  }

  // Read HMC5883L with improved error handling
  if (hmc5883l_available) {
    Vector mag = compass.readNormalize();
    // Check for valid readings (HMC5883L sometimes returns 0 on error)
    if (mag.XAxis != 0 || mag.YAxis != 0 || mag.ZAxis != 0) {
      sensors.magX = mag.XAxis - magOffsetX;
      sensors.magY = mag.YAxis - magOffsetY;
      sensors.magZ = mag.ZAxis - magOffsetZ;

      // Calculate heading
      sensors.heading = atan2(sensors.magY, sensors.magX) * 180.0 / PI;
      if (sensors.heading < 0) sensors.heading += 360;

      consecutiveHMCErrors = 0;  // Reset error counter on successful read

      // Apply magnetometer calibration if available
      if (magCal.valid) {
        // Apply hard iron correction
        float magX_corrected = sensors.magX - magCal.offsetX;
        float magY_corrected = sensors.magY - magCal.offsetY;
        float magZ_corrected = sensors.magZ - magCal.offsetZ;

        // Apply soft iron correction
        magX_corrected *= magCal.scaleX;
        magY_corrected *= magCal.scaleY;
        magZ_corrected *= magCal.scaleZ;

        // Recalculate heading with corrected values
        sensors.heading = atan2(magY_corrected, magX_corrected) * 180.0 / PI;
        if (sensors.heading < 0) sensors.heading += 360;

        // Apply magnetic declination
        sensors.heading += magCal.declination;
        if (sensors.heading >= 360) sensors.heading -= 360;
        if (sensors.heading < 0) sensors.heading += 360;
      }
    } else {
      consecutiveHMCErrors++;
      sensorErrors++;
      if (sensorErrors % 100 == 0) {  // Print error every 100 failures
        Serial.print("HMC5883L read error count: ");
        Serial.println(sensorErrors);
      }

      if (consecutiveHMCErrors > 50) {  // Disable after 50 consecutive errors
        Serial.println("Too many HMC5883L errors, disabling magnetometer");
        hmc5883l_available = false;
      }
    }
  }
  
  // Use gyro for heading if magnetometer not available or disabled
  if (!hmc5883l_available) {
    static float gyroHeading = 0;
    gyroHeading += sensors.gyroZ * 0.01;  // Integrate gyro Z for heading
    if (gyroHeading < 0) gyroHeading += 360;
    if (gyroHeading >= 360) gyroHeading -= 360;
    sensors.heading = gyroHeading;
  }

  // Read MS5611 with error handling
  if (ms5611_available) {
    int result = barometer.read();
    if (result == MS5611_READ_OK) {
      sensors.pressure = barometer.getPressure();
      sensors.temperature = barometer.getTemperature();

      // Calculate altitude from pressure (standard atmosphere formula)
      sensors.altitude = 44330.0 * (1.0 - pow(sensors.pressure / baselinePressure, 0.1903));
    } else {
      sensorErrors++;
      if (sensorErrors % 100 == 0) {  // Print error every 100 failures
        Serial.print("MS5611 read error: ");
        Serial.println(result);
      }
    }
  } else {
    sensors.altitude = 0;  // Set to 0 if no barometer
  }

  // Read battery voltage (this doesn't use I2C)
  int batteryRaw = analogRead(BATTERY_PIN);
  sensors.batteryVoltage = (batteryRaw / 4095.0) * 3.3 * 4.0;  // Voltage divider
}

void readRCInputs() {
  unsigned long currentTime = millis();
  rcInput.totalReads++;

  // Read PWM signals
  rcInput.throttle = pulseIn(RC_CH1_PIN, HIGH, 25000);  // Pin 14
  rcInput.roll = pulseIn(RC_CH2_PIN, HIGH, 25000);      // Pin 13
  rcInput.pitch = pulseIn(RC_CH3_PIN, HIGH, 25000);     // Pin 12
  rcInput.yaw = pulseIn(RC_CH4_PIN, HIGH, 25000);       // Pin 27
  rcInput.aux1 = pulseIn(RC_CH5_PIN, HIGH, 25000);      // Pin 26 (Flight mode - not used)
  rcInput.aux2 = pulseIn(RC_CH6_PIN, HIGH, 25000);      // Pin 25 (Aux 1)
  rcInput.aux3 = pulseIn(RC_CH7_PIN, HIGH, 25000);      // Pin 33 (Aux 2)
  rcInput.aux4 = pulseIn(RC_CH8_PIN, HIGH, 25000);      // Pin 34 (Aux 3 - 3-position switch)

  // Apply RC calibration if available
  if (rcInput.calibrationComplete) {
    rcInput.throttle = map(rcInput.throttle, rcInput.throttleMin, rcInput.throttleMax, RC_MIN, RC_MAX);
    rcInput.roll = map(rcInput.roll, rcInput.rollMin, rcInput.rollMax, RC_MIN, RC_MAX);
    rcInput.pitch = map(rcInput.pitch, rcInput.pitchMin, rcInput.pitchMax, RC_MIN, RC_MAX);
    rcInput.yaw = map(rcInput.yaw, rcInput.yawMin, rcInput.yawMax, RC_MIN, RC_MAX);
  }

  // Check for valid signals
  bool validSignal = (rcInput.throttle > 800 && rcInput.throttle < 2200 &&
                      rcInput.roll > 800 && rcInput.roll < 2200 &&
                      rcInput.pitch > 800 && rcInput.pitch < 2200 &&
                      rcInput.yaw > 800 && rcInput.yaw < 2200);

  if (validSignal) {
    rcInput.signalLost = false;
    rcInput.lastUpdate = currentTime;
    rcInput.lastGoodSignal = currentTime;
  } else {
    rcInput.failedReads++;
    // Check if signal was lost
    if (currentTime - rcInput.lastUpdate > 1000) {  // 1 second timeout
      rcInput.signalLost = true;
    }
  }

  // Calculate signal quality (percentage of good reads in last 100 attempts)
  if (rcInput.totalReads > 0) {
    int goodReads = rcInput.totalReads - rcInput.failedReads;
    rcInput.signalQuality = (goodReads * 100) / rcInput.totalReads;

    // Reset counters every 1000 reads to prevent overflow
    if (rcInput.totalReads >= 1000) {
      rcInput.totalReads = 100;
      rcInput.failedReads = rcInput.failedReads * 100 / 1000;  // Scale down proportionally
    }
  }
}

void updateGPSData() {
  if (gps.location.isValid()) {
    sensors.latitude = gps.location.lat();
    sensors.longitude = gps.location.lng();
    sensors.gpsValid = true;
  } else {
    sensors.gpsValid = false;
  }

  if (gps.altitude.isValid()) {
    sensors.gpsAltitude = gps.altitude.meters();
  }

  sensors.satellites = gps.satellites.value();
}

// ===== FLIGHT CONTROL FUNCTIONS =====

void calculateAttitude() {
  static float rollAngle = 0, pitchAngle = 0, yawAngle = 0;
  static unsigned long lastTime = 0;
  static bool firstRun = true;

  unsigned long currentTime = micros();
  float dt = (currentTime - lastTime) / 1000000.0;

  // Skip first iteration to avoid huge dt
  if (firstRun) {
    lastTime = currentTime;
    firstRun = false;
    return;
  }
  lastTime = currentTime;

  // Complementary filter for attitude estimation
  float accelRoll = atan2(sensors.accelY, sensors.accelZ) * 180.0 / PI;
  float accelPitch = atan2(-sensors.accelX, sqrt(sensors.accelY * sensors.accelY + sensors.accelZ * sensors.accelZ)) * 180.0 / PI;

  // Integrate gyroscope data
  rollAngle += sensors.gyroX * dt;
  pitchAngle += sensors.gyroY * dt;
  yawAngle += sensors.gyroZ * dt;  // Integrate yaw from gyro Z-axis

  // Apply complementary filter (98% gyro, 2% accel for roll/pitch)
  sensors.roll = 0.98 * rollAngle + 0.02 * accelRoll;
  sensors.pitch = 0.98 * pitchAngle + 0.02 * accelPitch;

  // For yaw, use gyro integration with magnetometer correction (if available)
  if (hmc5883l_available && abs(sensors.heading) > 0.1) {
    // Use complementary filter with magnetometer for yaw drift correction
    sensors.yaw = 0.95 * yawAngle + 0.05 * sensors.heading;
  } else {
    // Pure gyro integration if magnetometer not available
    sensors.yaw = yawAngle;
  }

  // Keep yaw in 0-360 range
  if (sensors.yaw < 0) sensors.yaw += 360;
  if (sensors.yaw >= 360) sensors.yaw -= 360;

  // Update angles for next iteration
  rollAngle = sensors.roll;
  pitchAngle = sensors.pitch;
  yawAngle = sensors.yaw;
}

void runPIDControllers() {
  if (!armed) return;

  static unsigned long lastTime = 0;
  unsigned long currentTime = micros();
  float dt = (currentTime - lastTime) / 1000000.0;
  lastTime = currentTime;

  // Calculate setpoints from RC inputs
  float rollSetpoint = map(rcInput.roll, RC_MIN, RC_MAX, -30, 30);  // ±30 degrees
  float pitchSetpoint = map(rcInput.pitch, RC_MIN, RC_MAX, -30, 30);
  float yawSetpoint = map(rcInput.yaw, RC_MIN, RC_MAX, -180, 180);  // Rate mode

  // Apply deadband
  if (abs(rcInput.roll - RC_MID) < RC_DEADBAND) rollSetpoint = 0;
  if (abs(rcInput.pitch - RC_MID) < RC_DEADBAND) pitchSetpoint = 0;
  if (abs(rcInput.yaw - RC_MID) < RC_DEADBAND) yawSetpoint = 0;

  // Run PID calculations
  pidRoll.output = calculatePID(&pidRoll, rollSetpoint, sensors.roll, dt);
  pidPitch.output = calculatePID(&pidPitch, pitchSetpoint, sensors.pitch, dt);
  pidYaw.output = calculatePID(&pidYaw, yawSetpoint, sensors.gyroZ, dt);  // Rate mode

  // Altitude hold (if enabled)
  if (currentMode == ALTITUDE_HOLD || currentMode == GPS_HOLD) {
    static float altitudeSetpoint = sensors.altitude;
    if (abs(rcInput.throttle - RC_MID) > RC_DEADBAND) {
      altitudeSetpoint = sensors.altitude;  // Update setpoint when throttle moved
    }
    pidAlt.output = calculatePID(&pidAlt, altitudeSetpoint, sensors.altitude, dt);
  }
}

float calculatePID(PIDController* pid, float setpoint, float input, float dt) {
  float error = setpoint - input;

  // Proportional term
  float pTerm = pid->kp * error;

  // Integral term
  pid->integral += error * dt;
  pid->integral = constrain(pid->integral, -100, 100);  // Prevent windup
  float iTerm = pid->ki * pid->integral;

  // Derivative term
  float dTerm = pid->kd * (error - pid->lastError) / dt;
  pid->lastError = error;

  // Calculate output
  float output = pTerm + iTerm + dTerm;
  return constrain(output, pid->outputMin, pid->outputMax);
}

void calculateMotorOutputs() {
  if (!armed) {
    motor1Speed = MOTOR_MIN;
    motor2Speed = MOTOR_MIN;
    motor3Speed = MOTOR_MIN;
    motor4Speed = MOTOR_MIN;
    return;
  }

  // Base throttle from RC input
  int baseThrottle = map(rcInput.throttle, RC_MIN, RC_MAX, MOTOR_IDLE, MOTOR_MAX);

  // Add altitude hold correction if enabled
  if (currentMode == ALTITUDE_HOLD || currentMode == GPS_HOLD) {
    baseThrottle += pidAlt.output;
  }

  // Mix PID outputs with throttle
  // Motor layout: 1=FR, 2=FL, 3=RL, 4=RR
  motor1Speed = baseThrottle - pidRoll.output + pidPitch.output - pidYaw.output;  // Front Right
  motor2Speed = baseThrottle + pidRoll.output + pidPitch.output + pidYaw.output;  // Front Left
  motor3Speed = baseThrottle + pidRoll.output - pidPitch.output - pidYaw.output;  // Rear Left
  motor4Speed = baseThrottle - pidRoll.output - pidPitch.output + pidYaw.output;  // Rear Right

  // Constrain motor speeds
  motor1Speed = constrain(motor1Speed, MOTOR_MIN, MOTOR_MAX);
  motor2Speed = constrain(motor2Speed, MOTOR_MIN, MOTOR_MAX);
  motor3Speed = constrain(motor3Speed, MOTOR_MIN, MOTOR_MAX);
  motor4Speed = constrain(motor4Speed, MOTOR_MIN, MOTOR_MAX);
}

void updateMotors() {
  motor1.writeMicroseconds(motor1Speed);
  motor2.writeMicroseconds(motor2Speed);
  motor3.writeMicroseconds(motor3Speed);
  motor4.writeMicroseconds(motor4Speed);
}

// ===== FLIGHT MODE AND SAFETY FUNCTIONS =====

void updateFlightMode() {
  // Check arming conditions
  if (!armed && rcInput.throttle < RC_MIN + 50 &&
      rcInput.yaw > RC_MAX - 50 && !rcInput.signalLost) {
    // Arm sequence: throttle low, yaw right
    armed = true;
    Serial.println("ARMED");
  } else if (armed && rcInput.throttle < RC_MIN + 50 &&
             rcInput.yaw < RC_MIN + 50) {
    // Disarm sequence: throttle low, yaw left
    armed = false;
    Serial.println("DISARMED");
  }

  // Flight mode switching based on AUX3 channel (3-position switch on pin 34)
  // AUX3 ranges: Low (1000-1400), Mid (1400-1600), High (1600-2000)
  if (rcInput.aux4 < 1400) {
    // Position 1: Manual mode (no stabilization)
    currentMode = MANUAL;
  } else if (rcInput.aux4 < 1600) {
    // Position 2: Stabilize mode (attitude stabilization)
    currentMode = STABILIZE;
  } else {
    // Position 3: Altitude Hold mode (stabilization + altitude hold)
    currentMode = ALTITUDE_HOLD;
  }

  // Debug flight mode changes
  static FlightMode lastMode = MANUAL;
  if (currentMode != lastMode) {
    Serial.print("Flight mode changed to: ");
    switch (currentMode) {
      case MANUAL: Serial.println("MANUAL"); break;
      case STABILIZE: Serial.println("STABILIZE"); break;
      case ALTITUDE_HOLD: Serial.println("ALTITUDE_HOLD"); break;
      case GPS_HOLD: Serial.println("GPS_HOLD"); break;
      case RETURN_TO_HOME: Serial.println("RETURN_TO_HOME"); break;
      case FAILSAFE: Serial.println("FAILSAFE"); break;
    }
    lastMode = currentMode;
  }

  // Override with failsafe if needed
  if (emergencyLanding) {
    currentMode = FAILSAFE;
  }
}

void checkFailSafe() {
  // RC signal loss
  if (rcInput.signalLost && armed) {
    if (rcLostTime == 0) {
      rcLostTime = millis();
    } else if (millis() - rcLostTime > 3000) {  // 3 second timeout
      emergencyLanding = true;
      Serial.println("FAILSAFE: RC SIGNAL LOST");
    }
  } else {
    rcLostTime = 0;
  }

  // Low battery
  if (sensors.batteryVoltage < 10.5 && armed) {  // 3S LiPo low voltage
    emergencyLanding = true;
    Serial.println("FAILSAFE: LOW BATTERY");
  }

  // Extreme attitude
  if ((abs(sensors.roll) > 60 || abs(sensors.pitch) > 60) && armed) {
    emergencyLanding = true;
    Serial.println("FAILSAFE: EXTREME ATTITUDE");
  }

  // Emergency landing procedure
  if (emergencyLanding && armed) {
    // Gradually reduce throttle
    static int emergencyThrottle = rcInput.throttle;
    emergencyThrottle -= 2;  // Reduce by 2 per loop cycle
    if (emergencyThrottle < MOTOR_IDLE) emergencyThrottle = MOTOR_IDLE;

    // Override motor outputs for controlled descent
    motor1Speed = emergencyThrottle;
    motor2Speed = emergencyThrottle;
    motor3Speed = emergencyThrottle;
    motor4Speed = emergencyThrottle;
  }
}

void setHomePosition() {
  static bool homeSet = false;
  if (!homeSet && sensors.gpsValid && sensors.satellites >= 6) {
    homeLatitude = sensors.latitude;
    homeLongitude = sensors.longitude;
    homeAltitude = sensors.altitude;
    homeSet = true;
    Serial.println("Home position set");
  }
}

// ===== CALIBRATION FUNCTIONS =====

void calibrateSensors() {
  Serial.println("Starting sensor calibration...");
  displayMessage("CALIBRATING...");

  // Check if we need full calibration or just basic setup
  bool needsFullCalibration = (!imuCal.valid || !magCal.valid || !baroCal.valid);

  if (needsFullCalibration) {
    Serial.println("Performing full sensor calibration...");

    // Calibrate accelerometer and gyroscope
    calibrateIMU();

    // Calibrate magnetometer
    calibrateMagnetometer();

    // Set baseline pressure for altitude reference
    calibrateBarometer();

    // Save all calibration data
    saveSensorCalibration();
  } else {
    Serial.println("Using saved calibration data");

    // Just update baseline pressure for current conditions
    calibrateBarometer();
  }

  Serial.println("Calibration complete!");
  displayMessage("CALIBRATION DONE");
  delay(2000);
}

void forceFullCalibration() {
  Serial.println("Forcing full sensor recalibration...");

  // Mark all calibrations as invalid to force recalibration
  imuCal.valid = false;
  magCal.valid = false;
  baroCal.valid = false;

  // Run full calibration
  calibrateSensors();
}

void calibrateIMU() {
  Serial.println("Starting enhanced IMU calibration...");
  Serial.println("This will perform 6-point accelerometer calibration");

  // Initialize calibration structure
  imuCal.accelOffsetX = 0;
  imuCal.accelOffsetY = 0;
  imuCal.accelOffsetZ = 0;
  imuCal.accelScaleX = 1.0;
  imuCal.accelScaleY = 1.0;
  imuCal.accelScaleZ = 1.0;

  // Gyroscope calibration (level position)
  Serial.println("Gyroscope calibration - Keep level and still");
  displayMessage("GYRO CAL");

  float gyroSumX = 0, gyroSumY = 0, gyroSumZ = 0;
  int samples = 1000;

  for (int i = 0; i < samples; i++) {
    mpu.update();
    gyroSumX += mpu.getGyroX();
    gyroSumY += mpu.getGyroY();
    gyroSumZ += mpu.getGyroZ();
    delay(5);
  }

  imuCal.gyroOffsetX = gyroSumX / samples;
  imuCal.gyroOffsetY = gyroSumY / samples;
  imuCal.gyroOffsetZ = gyroSumZ / samples;

  // Update legacy variables
  gyroOffsetX = imuCal.gyroOffsetX;
  gyroOffsetY = imuCal.gyroOffsetY;
  gyroOffsetZ = imuCal.gyroOffsetZ;

  // 6-point accelerometer calibration
  float accelReadings[6][3];  // 6 orientations, 3 axes
  const char* orientations[] = {
    "Level (Z up)",
    "Upside down (Z down)",
    "Right side (Y up)",
    "Left side (Y down)",
    "Nose up (X up)",
    "Nose down (X down)"
  };

  for (int orientation = 0; orientation < 6; orientation++) {
    Serial.print("Position "); Serial.print(orientation + 1); Serial.print("/6: ");
    Serial.println(orientations[orientation]);
    Serial.println("Press AUX1 when ready...");

    displayMessage(orientations[orientation]);

    // Wait for AUX1 press
    while (rcInput.aux1 < 1700) {
      readRCInputs();
      delay(100);
    }

    // Wait for AUX1 release
    while (rcInput.aux1 > 1300) {
      readRCInputs();
      delay(100);
    }

    Serial.println("Measuring...");
    delay(1000);  // Settle time

    // Take measurements
    float sumX = 0, sumY = 0, sumZ = 0;
    for (int i = 0; i < 200; i++) {
      mpu.update();
      sumX += mpu.getAccX();
      sumY += mpu.getAccY();
      sumZ += mpu.getAccZ();
      delay(10);
    }

    accelReadings[orientation][0] = sumX / 200.0;
    accelReadings[orientation][1] = sumY / 200.0;
    accelReadings[orientation][2] = sumZ / 200.0;

    Serial.print("Readings: X="); Serial.print(accelReadings[orientation][0]);
    Serial.print(" Y="); Serial.print(accelReadings[orientation][1]);
    Serial.print(" Z="); Serial.println(accelReadings[orientation][2]);
  }

  // Calculate offsets and scales
  imuCal.accelOffsetX = (accelReadings[4][0] + accelReadings[5][0]) / 2.0;  // X up + X down
  imuCal.accelOffsetY = (accelReadings[2][1] + accelReadings[3][1]) / 2.0;  // Y up + Y down
  imuCal.accelOffsetZ = (accelReadings[0][2] + accelReadings[1][2]) / 2.0;  // Z up + Z down

  imuCal.accelScaleX = 2.0 / (accelReadings[4][0] - accelReadings[5][0]);  // 2g / range
  imuCal.accelScaleY = 2.0 / (accelReadings[2][1] - accelReadings[3][1]);
  imuCal.accelScaleZ = 2.0 / (accelReadings[0][2] - accelReadings[1][2]);

  // Update legacy variables
  accelOffsetX = imuCal.accelOffsetX;
  accelOffsetY = imuCal.accelOffsetY;
  accelOffsetZ = imuCal.accelOffsetZ;

  // Store reference temperature
  imuCal.temperature = sensors.temperature;
  imuCal.valid = true;

  Serial.println("Enhanced IMU calibration complete!");
  Serial.print("Accel offsets: X="); Serial.print(imuCal.accelOffsetX);
  Serial.print(" Y="); Serial.print(imuCal.accelOffsetY);
  Serial.print(" Z="); Serial.println(imuCal.accelOffsetZ);
  Serial.print("Accel scales: X="); Serial.print(imuCal.accelScaleX);
  Serial.print(" Y="); Serial.print(imuCal.accelScaleY);
  Serial.print(" Z="); Serial.println(imuCal.accelScaleZ);
}

void calibrateMagnetometer() {
  Serial.println("Enhanced magnetometer calibration starting...");
  Serial.println("Rotate quadcopter in all directions for 60 seconds");
  Serial.println("Try to cover all orientations smoothly");

  displayMessage("MAG CAL - ROTATE");

  // Data collection arrays
  const int maxSamples = 1000;
  float magData[maxSamples][3];
  int sampleCount = 0;

  float magMinX = 1000, magMaxX = -1000;
  float magMinY = 1000, magMaxY = -1000;
  float magMinZ = 1000, magMaxZ = -1000;

  unsigned long startTime = millis();
  unsigned long lastSample = 0;

  while (millis() - startTime < 60000 && sampleCount < maxSamples) {  // 60 second calibration
    if (millis() - lastSample > 60) {  // Sample every 60ms
      Vector mag = compass.readNormalize();
      if (mag.XAxis != 0 || mag.YAxis != 0 || mag.ZAxis != 0) {
        float x = mag.XAxis;
        float y = mag.YAxis;
        float z = mag.ZAxis;

        // Store sample for sphere fitting
        if (sampleCount < maxSamples) {
          magData[sampleCount][0] = x;
          magData[sampleCount][1] = y;
          magData[sampleCount][2] = z;
          sampleCount++;
        }

        // Track min/max for basic calibration
        if (x < magMinX) magMinX = x;
        if (x > magMaxX) magMaxX = x;
        if (y < magMinY) magMinY = y;
        if (y > magMaxY) magMaxY = y;
        if (z < magMinZ) magMinZ = z;
        if (z > magMaxZ) magMaxZ = z;

        lastSample = millis();

        // Progress indicator
        if (sampleCount % 50 == 0) {
          Serial.print("Samples: "); Serial.println(sampleCount);
        }
      }
    }
    delay(10);
  }

  Serial.print("Collected "); Serial.print(sampleCount); Serial.println(" samples");

  // Basic calibration (hard iron correction)
  magCal.offsetX = (magMaxX + magMinX) / 2.0;
  magCal.offsetY = (magMaxY + magMinY) / 2.0;
  magCal.offsetZ = (magMaxZ + magMinZ) / 2.0;

  // Simple soft iron correction (ellipsoid to sphere)
  float rangeX = magMaxX - magMinX;
  float rangeY = magMaxY - magMinY;
  float rangeZ = magMaxZ - magMinZ;
  float avgRange = (rangeX + rangeY + rangeZ) / 3.0;

  magCal.scaleX = avgRange / rangeX;
  magCal.scaleY = avgRange / rangeY;
  magCal.scaleZ = avgRange / rangeZ;

  // Set magnetic declination (you should update this for your location)
  // This is a placeholder - get actual declination from: https://www.magnetic-declination.com/
  magCal.declination = 0.0;  // Update for your location

  // Update legacy variables
  magOffsetX = magCal.offsetX;
  magOffsetY = magCal.offsetY;
  magOffsetZ = magCal.offsetZ;

  magCal.valid = true;

  Serial.println("Enhanced magnetometer calibration complete!");
  Serial.print("Hard iron offsets: X="); Serial.print(magCal.offsetX);
  Serial.print(" Y="); Serial.print(magCal.offsetY);
  Serial.print(" Z="); Serial.println(magCal.offsetZ);
  Serial.print("Soft iron scales: X="); Serial.print(magCal.scaleX);
  Serial.print(" Y="); Serial.print(magCal.scaleY);
  Serial.print(" Z="); Serial.println(magCal.scaleZ);
  Serial.println("Note: Update magnetic declination for your location!");
}

// ===== RC CALIBRATION FUNCTIONS =====

void loadRCCalibration() {
  preferences.begin("rc_cal", true);  // Read-only mode

  // Check if calibration data exists
  if (preferences.getUShort("magic", 0) == EEPROM_MAGIC_NUMBER) {
    rcInput.throttleMin = preferences.getUShort("thr_min", RC_MIN);
    rcInput.throttleMax = preferences.getUShort("thr_max", RC_MAX);
    rcInput.rollMin = preferences.getUShort("roll_min", RC_MIN);
    rcInput.rollMax = preferences.getUShort("roll_max", RC_MAX);
    rcInput.rollCenter = preferences.getUShort("roll_cen", RC_MID);
    rcInput.pitchMin = preferences.getUShort("pit_min", RC_MIN);
    rcInput.pitchMax = preferences.getUShort("pit_max", RC_MAX);
    rcInput.pitchCenter = preferences.getUShort("pit_cen", RC_MID);
    rcInput.yawMin = preferences.getUShort("yaw_min", RC_MIN);
    rcInput.yawMax = preferences.getUShort("yaw_max", RC_MAX);
    rcInput.yawCenter = preferences.getUShort("yaw_cen", RC_MID);

    rcInput.calibrationComplete = true;
    Serial.println("RC calibration loaded from storage");
  } else {
    // Set default values
    rcInput.throttleMin = RC_MIN;
    rcInput.throttleMax = RC_MAX;
    rcInput.rollMin = RC_MIN;
    rcInput.rollMax = RC_MAX;
    rcInput.rollCenter = RC_MID;
    rcInput.pitchMin = RC_MIN;
    rcInput.pitchMax = RC_MAX;
    rcInput.pitchCenter = RC_MID;
    rcInput.yawMin = RC_MIN;
    rcInput.yawMax = RC_MAX;
    rcInput.yawCenter = RC_MID;

    rcInput.calibrationComplete = false;
    Serial.println("No RC calibration found, using defaults");
  }

  preferences.end();
}

void saveRCCalibration() {
  preferences.begin("rc_cal", false);  // Read-write mode

  preferences.putUShort("magic", EEPROM_MAGIC_NUMBER);
  preferences.putUShort("thr_min", rcInput.throttleMin);
  preferences.putUShort("thr_max", rcInput.throttleMax);
  preferences.putUShort("roll_min", rcInput.rollMin);
  preferences.putUShort("roll_max", rcInput.rollMax);
  preferences.putUShort("roll_cen", rcInput.rollCenter);
  preferences.putUShort("pit_min", rcInput.pitchMin);
  preferences.putUShort("pit_max", rcInput.pitchMax);
  preferences.putUShort("pit_cen", rcInput.pitchCenter);
  preferences.putUShort("yaw_min", rcInput.yawMin);
  preferences.putUShort("yaw_max", rcInput.yawMax);
  preferences.putUShort("yaw_cen", rcInput.yawCenter);

  preferences.end();

  rcInput.calibrationComplete = true;
  Serial.println("RC calibration saved to storage");
}

void startRCCalibration() {
  Serial.println("Starting RC calibration...");
  Serial.println("Move all sticks to extremes, then center them");
  Serial.println("Press AUX1 switch when done");

  rcInput.calibrationMode = true;

  // Initialize min/max values
  rcInput.throttleMin = 2000;
  rcInput.throttleMax = 1000;
  rcInput.rollMin = 2000;
  rcInput.rollMax = 1000;
  rcInput.pitchMin = 2000;
  rcInput.pitchMax = 1000;
  rcInput.yawMin = 2000;
  rcInput.yawMax = 1000;

  displayMessage("RC CALIBRATION");
}

void updateRCCalibration() {
  if (!rcInput.calibrationMode) return;

  // Update min/max values
  if (rcInput.throttle < rcInput.throttleMin) rcInput.throttleMin = rcInput.throttle;
  if (rcInput.throttle > rcInput.throttleMax) rcInput.throttleMax = rcInput.throttle;
  if (rcInput.roll < rcInput.rollMin) rcInput.rollMin = rcInput.roll;
  if (rcInput.roll > rcInput.rollMax) rcInput.rollMax = rcInput.roll;
  if (rcInput.pitch < rcInput.pitchMin) rcInput.pitchMin = rcInput.pitch;
  if (rcInput.pitch > rcInput.pitchMax) rcInput.pitchMax = rcInput.pitch;
  if (rcInput.yaw < rcInput.yawMin) rcInput.yawMin = rcInput.yaw;
  if (rcInput.yaw > rcInput.yawMax) rcInput.yawMax = rcInput.yaw;

  // Check if calibration is complete (AUX1 switch pressed)
  if (rcInput.aux1 > 1700) {  // AUX1 high position
    // Calculate center positions
    rcInput.rollCenter = (rcInput.rollMin + rcInput.rollMax) / 2;
    rcInput.pitchCenter = (rcInput.pitchMin + rcInput.pitchMax) / 2;
    rcInput.yawCenter = (rcInput.yawMin + rcInput.yawMax) / 2;

    rcInput.calibrationMode = false;
    saveRCCalibration();

    Serial.println("RC calibration complete!");
    Serial.print("Throttle: "); Serial.print(rcInput.throttleMin); Serial.print("-"); Serial.println(rcInput.throttleMax);
    Serial.print("Roll: "); Serial.print(rcInput.rollMin); Serial.print("-"); Serial.print(rcInput.rollMax); Serial.print(" (center: "); Serial.print(rcInput.rollCenter); Serial.println(")");
    Serial.print("Pitch: "); Serial.print(rcInput.pitchMin); Serial.print("-"); Serial.print(rcInput.pitchMax); Serial.print(" (center: "); Serial.print(rcInput.pitchCenter); Serial.println(")");
    Serial.print("Yaw: "); Serial.print(rcInput.yawMin); Serial.print("-"); Serial.print(rcInput.yawMax); Serial.print(" (center: "); Serial.print(rcInput.yawCenter); Serial.println(")");

    displayMessage("RC CAL DONE");
    delay(2000);
  }
}

// ===== ENHANCED SENSOR CALIBRATION FUNCTIONS =====

void loadSensorCalibration() {
  // Load IMU calibration
  preferences.begin("imu_cal", true);
  if (preferences.getUShort("magic", 0) == EEPROM_MAGIC_NUMBER) {
    imuCal.accelOffsetX = preferences.getFloat("acc_ox", 0);
    imuCal.accelOffsetY = preferences.getFloat("acc_oy", 0);
    imuCal.accelOffsetZ = preferences.getFloat("acc_oz", 0);
    imuCal.accelScaleX = preferences.getFloat("acc_sx", 1.0);
    imuCal.accelScaleY = preferences.getFloat("acc_sy", 1.0);
    imuCal.accelScaleZ = preferences.getFloat("acc_sz", 1.0);
    imuCal.gyroOffsetX = preferences.getFloat("gyr_ox", 0);
    imuCal.gyroOffsetY = preferences.getFloat("gyr_oy", 0);
    imuCal.gyroOffsetZ = preferences.getFloat("gyr_oz", 0);
    imuCal.temperature = preferences.getFloat("temp", 25.0);
    imuCal.valid = true;

    // Update legacy variables
    accelOffsetX = imuCal.accelOffsetX;
    accelOffsetY = imuCal.accelOffsetY;
    accelOffsetZ = imuCal.accelOffsetZ;
    gyroOffsetX = imuCal.gyroOffsetX;
    gyroOffsetY = imuCal.gyroOffsetY;
    gyroOffsetZ = imuCal.gyroOffsetZ;

    Serial.println("IMU calibration loaded");
  } else {
    imuCal.valid = false;
    Serial.println("No IMU calibration found");
  }
  preferences.end();

  // Load Magnetometer calibration
  preferences.begin("mag_cal", true);
  if (preferences.getUShort("magic", 0) == EEPROM_MAGIC_NUMBER) {
    magCal.offsetX = preferences.getFloat("mag_ox", 0);
    magCal.offsetY = preferences.getFloat("mag_oy", 0);
    magCal.offsetZ = preferences.getFloat("mag_oz", 0);
    magCal.scaleX = preferences.getFloat("mag_sx", 1.0);
    magCal.scaleY = preferences.getFloat("mag_sy", 1.0);
    magCal.scaleZ = preferences.getFloat("mag_sz", 1.0);
    magCal.declination = preferences.getFloat("declination", 0);
    magCal.valid = true;

    // Update legacy variables
    magOffsetX = magCal.offsetX;
    magOffsetY = magCal.offsetY;
    magOffsetZ = magCal.offsetZ;

    Serial.println("Magnetometer calibration loaded");
  } else {
    magCal.valid = false;
    Serial.println("No magnetometer calibration found");
  }
  preferences.end();

  // Load Barometer calibration
  preferences.begin("baro_cal", true);
  if (preferences.getUShort("magic", 0) == EEPROM_MAGIC_NUMBER) {
    baroCal.pressureOffset = preferences.getFloat("press_off", 0);
    baroCal.temperatureOffset = preferences.getFloat("temp_off", 0);
    baroCal.seaLevelPressure = preferences.getFloat("sea_press", 101325.0);
    baroCal.valid = true;

    baselinePressure = baroCal.seaLevelPressure;
    Serial.println("Barometer calibration loaded");
  } else {
    baroCal.valid = false;
    Serial.println("No barometer calibration found");
  }
  preferences.end();
}

void saveSensorCalibration() {
  // Save IMU calibration
  preferences.begin("imu_cal", false);
  preferences.putUShort("magic", EEPROM_MAGIC_NUMBER);
  preferences.putFloat("acc_ox", imuCal.accelOffsetX);
  preferences.putFloat("acc_oy", imuCal.accelOffsetY);
  preferences.putFloat("acc_oz", imuCal.accelOffsetZ);
  preferences.putFloat("acc_sx", imuCal.accelScaleX);
  preferences.putFloat("acc_sy", imuCal.accelScaleY);
  preferences.putFloat("acc_sz", imuCal.accelScaleZ);
  preferences.putFloat("gyr_ox", imuCal.gyroOffsetX);
  preferences.putFloat("gyr_oy", imuCal.gyroOffsetY);
  preferences.putFloat("gyr_oz", imuCal.gyroOffsetZ);
  preferences.putFloat("temp", imuCal.temperature);
  preferences.end();

  // Save Magnetometer calibration
  preferences.begin("mag_cal", false);
  preferences.putUShort("magic", EEPROM_MAGIC_NUMBER);
  preferences.putFloat("mag_ox", magCal.offsetX);
  preferences.putFloat("mag_oy", magCal.offsetY);
  preferences.putFloat("mag_oz", magCal.offsetZ);
  preferences.putFloat("mag_sx", magCal.scaleX);
  preferences.putFloat("mag_sy", magCal.scaleY);
  preferences.putFloat("mag_sz", magCal.scaleZ);
  preferences.putFloat("declination", magCal.declination);
  preferences.end();

  // Save Barometer calibration
  preferences.begin("baro_cal", false);
  preferences.putUShort("magic", EEPROM_MAGIC_NUMBER);
  preferences.putFloat("press_off", baroCal.pressureOffset);
  preferences.putFloat("temp_off", baroCal.temperatureOffset);
  preferences.putFloat("sea_press", baroCal.seaLevelPressure);
  preferences.end();

  Serial.println("All sensor calibrations saved");
}

void calibrateBarometer() {
  Serial.println("Calibrating barometer baseline...");

  float pressureSum = 0;
  int samples = 100;

  for (int i = 0; i < samples; i++) {
    int result = barometer.read();
    if (result == MS5611_READ_OK) {
      pressureSum += barometer.getPressure();
    }
    delay(10);
  }

  baselinePressure = pressureSum / samples;
  Serial.print("Baseline pressure set to: ");
  Serial.print(baselinePressure);
  Serial.println(" Pa");
}

// ===== DISPLAY FUNCTIONS =====

void updateDisplay() {
  tft.fillScreen(ST7735_BLACK);
  tft.setCursor(0, 0);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);

  // Flight mode and status
  tft.print("Mode: ");
  switch (currentMode) {
    case MANUAL: tft.println("MANUAL"); break;
    case STABILIZE: tft.println("STAB"); break;
    case ALTITUDE_HOLD: tft.println("ALT HOLD"); break;
    case GPS_HOLD: tft.println("GPS HOLD"); break;
    case RETURN_TO_HOME: tft.println("RTH"); break;
    case FAILSAFE: tft.println("FAILSAFE"); break;
  }

  tft.print("Armed: ");
  tft.println(armed ? "YES" : "NO");

  // Attitude
  tft.print("Roll: ");
  tft.println(sensors.roll, 1);
  tft.print("Pitch: ");
  tft.println(sensors.pitch, 1);
  tft.print("Yaw: ");
  tft.println(sensors.yaw, 1);

  // Altitude and GPS
  tft.print("Alt: ");
  tft.print(sensors.altitude, 1);
  tft.println("m");

  tft.print("GPS: ");
  if (sensors.gpsValid) {
    tft.print(sensors.satellites);
    tft.println(" sats");
  } else {
    tft.println("NO FIX");
  }

  // Battery
  tft.print("Batt: ");
  tft.print(sensors.batteryVoltage, 1);
  tft.println("V");

  // RC status with signal quality
  tft.print("RC: ");
  if (rcInput.signalLost) {
    tft.setTextColor(ST7735_RED);
    tft.println("LOST");
  } else {
    // Color code signal quality
    if (rcInput.signalQuality >= 90) {
      tft.setTextColor(ST7735_GREEN);
    } else if (rcInput.signalQuality >= 70) {
      tft.setTextColor(ST7735_YELLOW);
    } else {
      tft.setTextColor(ST7735_RED);
    }
    tft.print(rcInput.signalQuality);
    tft.println("%");
  }
  tft.setTextColor(ST7735_WHITE);  // Reset color

  // RC calibration status
  tft.print("Cal: ");
  tft.println(rcInput.calibrationComplete ? "YES" : "NO");
}

void displayMessage(const char* message) {
  tft.fillScreen(ST7735_BLACK);
  tft.setCursor(10, 60);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(2);
  tft.println(message);
}

void displayError(const char* error) {
  tft.fillScreen(ST7735_RED);
  tft.setCursor(10, 60);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(2);
  tft.println(error);
}

// ===== UTILITY FUNCTIONS =====

float mapFloat(float x, float in_min, float in_max, float out_min, float out_max) {
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

void printDebugInfo() {
  // Print sensor data for debugging
  Serial.print("Roll: "); Serial.print(sensors.roll, 1);
  Serial.print(" Pitch: "); Serial.print(sensors.pitch, 1);
  Serial.print(" Yaw: "); Serial.print(sensors.yaw, 1);
  Serial.print(" GyroZ: "); Serial.print(sensors.gyroZ, 3);
  Serial.print(" Heading: "); Serial.print(sensors.heading, 1);
  Serial.print(" Alt: "); Serial.print(sensors.altitude, 1);
  Serial.print(" Batt: "); Serial.print(sensors.batteryVoltage, 1);
  Serial.print(" RC: "); Serial.print(rcInput.signalLost ? "LOST" : "OK");
  Serial.print(" Armed: "); Serial.println(armed ? "YES" : "NO");
}

void printYawDebug() {
  // Detailed yaw debugging
  Serial.print("GyroZ: "); Serial.print(sensors.gyroZ, 4);
  Serial.print(" Heading: "); Serial.print(sensors.heading, 2);
  Serial.print(" Yaw: "); Serial.print(sensors.yaw, 2);
  Serial.print(" HMC Available: "); Serial.println(hmc5883l_available ? "YES" : "NO");
}

void printRCDebug() {
  // RC channel debugging
  Serial.print("RC - Thr:"); Serial.print(rcInput.throttle);
  Serial.print(" Roll:"); Serial.print(rcInput.roll);
  Serial.print(" Pitch:"); Serial.print(rcInput.pitch);
  Serial.print(" Yaw:"); Serial.print(rcInput.yaw);
  Serial.print(" AUX3:"); Serial.print(rcInput.aux4);
  Serial.print(" Mode:");
  switch (currentMode) {
    case MANUAL: Serial.print("MAN"); break;
    case STABILIZE: Serial.print("STAB"); break;
    case ALTITUDE_HOLD: Serial.print("ALT"); break;
    case GPS_HOLD: Serial.print("GPS"); break;
    case RETURN_TO_HOME: Serial.print("RTH"); break;
    case FAILSAFE: Serial.print("FAIL"); break;
  }
  Serial.println();
}

// ===== ESC CALIBRATION FUNCTION =====
// Call this function once to calibrate ESCs
// Remove after calibration is complete
void calibrateESCs() {
  Serial.println("ESC Calibration Mode");
  Serial.println("Disconnect battery, press any key when ready...");

  while (!Serial.available()) {
    delay(100);
  }
  Serial.read();

  // Set all ESCs to maximum throttle
  motor1.writeMicroseconds(MOTOR_MAX);
  motor2.writeMicroseconds(MOTOR_MAX);
  motor3.writeMicroseconds(MOTOR_MAX);
  motor4.writeMicroseconds(MOTOR_MAX);

  Serial.println("Connect battery now, wait for beeps, then press any key...");

  while (!Serial.available()) {
    delay(100);
  }
  Serial.read();

  // Set all ESCs to minimum throttle
  motor1.writeMicroseconds(MOTOR_MIN);
  motor2.writeMicroseconds(MOTOR_MIN);
  motor3.writeMicroseconds(MOTOR_MIN);
  motor4.writeMicroseconds(MOTOR_MIN);

  Serial.println("ESC calibration complete!");
  delay(5000);
}

/*
 * USAGE INSTRUCTIONS:
 *
 * 1. HARDWARE SETUP:
 *    - Connect GY-86 module to I2C pins (SDA=21, SCL=22)
 *    - Connect GPS module to pins 16 (RX) and 17 (TX)
 *    - Connect RC receiver PWM outputs to pins 32-39
 *    - Connect ESCs to pins 2, 4, 5, 18
 *    - Connect TFT display via SPI
 *    - Connect battery voltage divider to pin 35
 *
 * 2. FIRST TIME SETUP:
 *    - Uncomment calibrateESCs() call in setup() for ESC calibration
 *    - Upload code and follow ESC calibration procedure
 *    - Comment out calibrateESCs() call and re-upload
 *
 * 3. OPERATION:
 *    - Power on and wait for sensor initialization
 *    - Wait for GPS fix (6+ satellites recommended)
 *    - Arm: Throttle low + Yaw right
 *    - Disarm: Throttle low + Yaw left
 *    - Flight modes via AUX1 channel:
 *      * Low: Manual mode
 *      * Mid: Stabilize mode
 *      * High: Altitude hold mode
 *
 * 4. SAFETY FEATURES:
 *    - Automatic disarm on RC signal loss
 *    - Low battery protection
 *    - Extreme attitude protection
 *    - Emergency landing mode
 *
 * 5. TUNING:
 *    - Adjust PID constants at top of file
 *    - Monitor serial output for debugging
 *    - Use TFT display for real-time status
 *
 * WARNING: This is experimental flight controller code.
 * Always test thoroughly in a safe environment.
 * Remove propellers during initial testing.
 * Follow all local regulations for drone operation.
 */

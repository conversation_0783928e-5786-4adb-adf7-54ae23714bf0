# QuadFly Flight Controller

A comprehensive Arduino-based quadcopter flight controller using ESP32 and multiple sensors.

## Hardware Requirements

### Main Components
- **ESP32 DevKit** - Main microcontroller
- **GY-86 10DOF Module** - Contains:
  - MPU6050 (Accelerometer + Gyroscope)
  - HMC5883L (Magnetometer)
  - MS5611 (Barometric Pressure Sensor)
- **NEO-6M GPS Module** - Position tracking
- **FS-TH9X RC Receiver** - Remote control input
- **1.8" TFT ST7735 Display** - Real-time flight data
- **4x ESCs** - Electronic Speed Controllers for motors
- **4x Brushless Motors** - Quadcopter propulsion
- **LiPo Battery** - Power supply (3S recommended)

### Wiring Diagram

#### I2C Connections (GY-86)
```
ESP32    GY-86
21   ->  SDA
22   ->  SCL
3.3V ->  VCC
GND  ->  GND
```

#### GPS Module
```
ESP32    NEO-6M
16   ->  TX
17   ->  RX
3.3V ->  VCC
GND  ->  <PERSON><PERSON>
```

#### RC Receiver (PWM)
```
ESP32    FS-TH9X
32   ->  CH1 (Throttle)
33   ->  CH2 (Roll)
25   ->  CH3 (Pitch)
26   ->  CH4 (Yaw)
27   ->  CH5 (Flight Mode)
14   ->  CH6 (Aux 1)
12   ->  CH7 (Aux 2)
13   ->  CH8 (Aux 3)
```

#### Motor ESCs
```
ESP32    ESC
2    ->  Motor 1 (Front Right)
4    ->  Motor 2 (Front Left)
5    ->  Motor 3 (Rear Left)
18   ->  Motor 4 (Rear Right)
```

#### TFT Display (SPI)
```
ESP32    ST7735
15   ->  CS
19   ->  RST
23   ->  DC/A0
23   ->  MOSI
18   ->  SCLK
3.3V ->  VCC
GND  ->  GND
```

#### Battery Monitor
```
ESP32    Battery
35   ->  Voltage Divider Output
```

## Required Libraries

Install these libraries through Arduino IDE Library Manager:

```
- MPU6050 by Electronic Cats
- HMC5883L by Korneliusz Jarzebski
- MS5611 by Rob Tillaart
- TinyGPS++ by Mikal Hart
- Adafruit GFX Library
- Adafruit ST7735 Library
- ESP32Servo by Kevin Harrington
```

## Setup Instructions

### 1. Hardware Assembly
1. Mount all components on quadcopter frame
2. Connect wiring according to diagram above
3. Ensure proper motor rotation directions:
   - Motors 1,3: Clockwise (CW)
   - Motors 2,4: Counter-clockwise (CCW)

### 2. Software Setup
1. Install Arduino IDE with ESP32 board support
2. Install required libraries
3. Open `quadcopter_flight_controller.ino`
4. Verify pin assignments match your wiring

### 3. ESC Calibration (First Time Only)
1. Uncomment `calibrateESCs();` in setup() function
2. Upload code to ESP32
3. Follow serial monitor instructions:
   - Disconnect battery
   - Press any key when ready
   - Connect battery, wait for ESC beeps
   - Press any key to complete calibration
4. Comment out `calibrateESCs();` and re-upload

### 4. Sensor Calibration
1. Place quadcopter on level surface
2. Power on and wait for automatic calibration
3. For magnetometer: rotate quadcopter in all directions for 30 seconds
4. Calibration values are automatically applied

## Operation

### Arming/Disarming
- **Arm**: Throttle low + Yaw stick full right (hold for 2 seconds)
- **Disarm**: Throttle low + Yaw stick full left (hold for 2 seconds)

### Flight Modes (AUX1 Channel)
- **Manual** (AUX1 < 1300): Direct motor control, no stabilization
- **Stabilize** (1300 < AUX1 < 1700): Attitude stabilization enabled
- **Altitude Hold** (AUX1 > 1700): Stabilization + altitude hold

### Controls
- **Throttle**: Vertical movement
- **Roll**: Left/right tilt
- **Pitch**: Forward/backward tilt
- **Yaw**: Rotation around vertical axis

### Display Information
The TFT display shows:
- Current flight mode
- Armed status
- Roll, pitch, yaw angles
- Altitude
- GPS status and satellite count
- Battery voltage
- RC signal status

## Safety Features

### Automatic Fail-safes
- **RC Signal Loss**: Emergency landing after 3 seconds
- **Low Battery**: Emergency landing when voltage < 10.5V
- **Extreme Attitude**: Emergency landing if roll/pitch > 60°
- **Sensor Failure**: Automatic mode switching or emergency landing

### Manual Safety
- Always remove propellers during testing
- Test in open area away from people
- Monitor battery voltage
- Have manual override ready

## Tuning

### PID Parameters
Adjust these constants in the code for your specific quadcopter:

```cpp
// Roll/Pitch PID
#define PID_ROLL_KP 1.5    // Proportional gain
#define PID_ROLL_KI 0.05   // Integral gain  
#define PID_ROLL_KD 0.8    // Derivative gain

// Yaw PID
#define PID_YAW_KP 2.0
#define PID_YAW_KI 0.1
#define PID_YAW_KD 0.5

// Altitude PID
#define PID_ALT_KP 3.0
#define PID_ALT_KI 0.1
#define PID_ALT_KD 1.0
```

### Tuning Process
1. Start with low gains
2. Increase P gain until oscillation begins
3. Reduce P gain by 20%
4. Add small amount of D gain to reduce oscillation
5. Add minimal I gain to eliminate steady-state error

## Troubleshooting

### Common Issues
- **Motors not spinning**: Check ESC calibration and wiring
- **Unstable flight**: Reduce PID gains, check sensor calibration
- **GPS not working**: Ensure clear sky view, check wiring
- **Display blank**: Verify SPI connections and library installation
- **RC not responding**: Check receiver binding and PWM connections

### Debug Mode
Enable serial debugging by uncommenting debug prints in the code. Monitor at 115200 baud rate.

## Legal Notice

⚠️ **WARNING**: This is experimental flight controller software. 

- Always test thoroughly in a safe environment
- Follow all local regulations for drone operation
- User assumes all responsibility for safe operation
- Remove propellers during initial testing and setup

## License

This project is open source. Use at your own risk.

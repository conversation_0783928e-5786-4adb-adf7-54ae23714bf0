# Arduino Library Requirements for QuadFly Flight Controller

# Install these libraries through Arduino IDE Library Manager:
# Tools -> Manage Libraries... -> Search for each library name

# Core ESP32 Libraries (usually pre-installed)
- Wire (I2C communication)
- SPI (SPI communication) 
- HardwareSerial (UART communication)

# Sensor Libraries
MPU6050_tockn by tockn
# Alternative: MPU6050 by Electronic Cats (if tockn version not available)

Adafruit HMC5883 Unified by Adafruit
# Provides unified sensor interface for magnetometer

MS5611 by <PERSON>
# Barometric pressure sensor library

# GPS Library
TinyGPS++ by <PERSON><PERSON> Hart
# Lightweight GPS parsing library

# Display Libraries
Adafruit GFX Library by Adafruit
# Core graphics library for displays

Ada<PERSON>ruit ST7735 and ST7789 Library by Adafruit
# Specific driver for ST7735 TFT displays

# Servo/Motor Control
ESP32Servo by <PERSON>
# ESP32-compatible servo library for ESC control

# Installation Instructions:

1. Open Arduino IDE
2. Go to Tools -> Manage Libraries...
3. Search for each library name listed above
4. Click "Install" for each library
5. Restart Arduino IDE after installation

# Alternative Installation via Library Manager URLs:
# If libraries are not found in Library Manager, you can install from GitHub:

# MPU6050_tockn: https://github.com/tockn/MPU6050_tockn
# Download ZIP and install via Sketch -> Include Library -> Add .ZIP Library

# Troubleshooting:
# - If compilation errors occur, ensure you have the latest ESP32 board package
# - Some libraries may have dependencies that auto-install
# - Check that your Arduino IDE supports ESP32 (install ESP32 board package first)

# ESP32 Board Package Installation:
# 1. File -> Preferences
# 2. Additional Board Manager URLs: https://dl.espressif.com/dl/package_esp32_index.json
# 3. Tools -> Board -> Boards Manager
# 4. Search "ESP32" and install "ESP32 by Espressif Systems"

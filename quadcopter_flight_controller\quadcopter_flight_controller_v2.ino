/*
 * QuadFly Flight Controller v2.0
 * Professional Quadcopter Flight Controller for ESP32
 * 
 * Features:
 * - Advanced PID Control with Web Tuning
 * - Multiple Flight Modes (Manual, Stabilize, Altitude Hold, GPS, Acro)
 * - WiFi Telemetry & Web Interface
 * - Data Logging & Performance Monitoring
 * - Enhanced Safety Systems
 * - Real-time Sensor Fusion
 * 
 * Hardware: ESP32 + GY-86 (MPU6050 + HMC5883L + MS5611) + GPS
 * Author: QuadFly Team
 * Version: 2.0
 */

// ===== CORE LIBRARIES =====
#include <Wire.h>
#include <SPI.h>
#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include <Preferences.h>
#include <ESP32Servo.h>

// ===== SENSOR LIBRARIES =====
#include <MPU6050_tockn.h>
#include <HMC5883L.h>
#include <MS5611.h>
#include <TinyGPS++.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>

// ===== PIN DEFINITIONS =====
// I2C Pins
#define SDA_PIN 21
#define SCL_PIN 22

// RC Input Pins (PWM)
#define RC_THROTTLE_PIN 14
#define RC_ROLL_PIN 13
#define RC_PITCH_PIN 12
#define RC_YAW_PIN 27
#define RC_AUX1_PIN 26
#define RC_AUX2_PIN 25
#define RC_AUX3_PIN 36
#define RC_AUX4_PIN 34

// Motor Output Pins (PWM)
#define MOTOR_FL_PIN 33  // Front Left
#define MOTOR_FR_PIN 32  // Front Right
#define MOTOR_BL_PIN 15  // Back Left
#define MOTOR_BR_PIN 19  // Back Right

// Display Pins (SPI)
#define TFT_CS_PIN 5
#define TFT_RST_PIN 4
#define TFT_DC_PIN 2

// Other Pins
#define LED_PIN 0
#define BATTERY_PIN 35
#define GPS_RX_PIN 16
#define GPS_TX_PIN 17

// ===== FLIGHT CONSTANTS =====
#define RC_MIN 1000
#define RC_MID 1500
#define RC_MAX 2000
#define RC_DEADBAND 50

#define MOTOR_MIN 1000
#define MOTOR_MAX 2000
#define MOTOR_IDLE 1100

// Default PID Values
#define PID_ROLL_KP 1.5
#define PID_ROLL_KI 0.05
#define PID_ROLL_KD 0.025

#define PID_PITCH_KP 1.5
#define PID_PITCH_KI 0.05
#define PID_PITCH_KD 0.025

#define PID_YAW_KP 2.0
#define PID_YAW_KI 0.1
#define PID_YAW_KD 0.01

#define PID_ALT_KP 1.2
#define PID_ALT_KI 0.3
#define PID_ALT_KD 0.2

// ===== FLIGHT MODES =====
enum FlightMode {
  MANUAL = 0,
  STABILIZE = 1,
  ALTITUDE_HOLD = 2,
  GPS_HOLD = 3,
  RETURN_TO_HOME = 4,
  ACRO = 5,
  CIRCLE = 6,
  AUTO_LAND = 7,
  FAILSAFE = 8
};

enum DisplayMode {
  DISPLAY_BASIC = 0,
  DISPLAY_ARTIFICIAL_HORIZON = 1,
  DISPLAY_GPS_MAP = 2,
  DISPLAY_PERFORMANCE = 3,
  DISPLAY_DIAGNOSTICS = 4
};

// ===== CORE STRUCTURES =====
struct PIDController {
  float kp, ki, kd;
  float setpoint, input, output;
  float integral, lastError, lastInput;
  float outputMin, outputMax;
  float integralMax;
  bool rateMode;
  float errorSum;
  int sampleCount;
  unsigned long lastUpdate;
};

struct SensorData {
  float roll, pitch, yaw;
  float gyroX, gyroY, gyroZ;
  float accelX, accelY, accelZ;
  float magX, magY, magZ;
  float altitude, pressure, temperature;
  float batteryVoltage;
  float heading;
  bool sensorsHealthy;
};

struct RCData {
  int throttle, roll, pitch, yaw;
  int aux1, aux2, aux3, aux4;
  bool signalLost;
  int signalQuality;
  unsigned long lastUpdate;
};

struct MotorOutputs {
  int frontLeft, frontRight;
  int backLeft, backRight;
  bool motorsArmed;
};

struct FlightState {
  FlightMode currentMode;
  DisplayMode displayMode;
  bool armed;
  bool calibrationMode;
  bool emergencyStop;
  unsigned long flightStartTime;
  unsigned long totalFlightTime;
};

struct PerformanceMetrics {
  float rollRMS, pitchRMS, yawRMS, altitudeRMS;
  float vibrationLevel;
  float loopTime;
  int loopTimeViolations;
  bool performanceLogging;
};

// ===== GLOBAL OBJECTS =====
// Sensors
MPU6050 mpu(Wire);
HMC5883L compass;
MS5611 barometer;
TinyGPSPlus gps;
Adafruit_ST7735 tft = Adafruit_ST7735(TFT_CS_PIN, TFT_DC_PIN, TFT_RST_PIN);

// Servos for ESCs
Servo motorFL, motorFR, motorBL, motorBR;

// WiFi & Web Server
WebServer webServer(80);
Preferences preferences;

// Serial for GPS
HardwareSerial gpsSerial(2);

// ===== GLOBAL VARIABLES =====
// Core flight data
SensorData sensors;
RCData rcInput;
MotorOutputs motors;
FlightState flightState;
PerformanceMetrics performance;

// PID Controllers
PIDController pidRoll, pidPitch, pidYaw, pidAlt;

// Sensor availability flags
bool mpu6050_available = false;
bool hmc5883l_available = false;
bool ms5611_available = false;
bool gps_available = false;

// Timing variables
unsigned long lastLoopTime = 0;
unsigned long displayUpdateTime = 0;
unsigned long telemetryUpdateTime = 0;
unsigned long logUpdateTime = 0;

// WiFi credentials
const char* wifi_ssid = "QuadFly_Telemetry";
const char* wifi_password = "quadfly123";

// ===== FORWARD DECLARATIONS =====
// Core functions
void initializeSystem();
void initializeSensors();
void initializeWiFi();
void initializePID();
void mainFlightLoop();

// Sensor functions
void readSensors();
void calibrateSensors();
bool checkSensorHealth();

// Flight control functions
void readRCInputs();
void processFlightMode();
void calculatePIDOutputs();
void updateMotors();
void checkSafetyConditions();

// PID functions
float calculatePID(PIDController* pid, float setpoint, float input, float dt);
void resetPIDIntegrals();
void savePIDSettings();
void loadPIDSettings();

// Display functions
void updateDisplay();
void drawArtificialHorizon();
void drawGPSMap();
void drawPerformanceDisplay();
void drawDiagnosticsDisplay();

// Web interface functions
void handleWebRequests();
void handleRoot();
void handleTelemetry();
void handlePIDSetup();
void handlePIDUpdate();
void handleLogs();

// Data logging functions
void initDataLogging();
void logFlightData();
void closeLogFile();

// Utility functions
void updatePerformanceMetrics();
void detectVibration();
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max);
void blinkLED(int times, int delayMs);

// ===== MAIN SETUP FUNCTION =====
void setup() {
  Serial.begin(115200);
  Serial.println("QuadFly Flight Controller v2.0 Starting...");
  
  // Initialize all systems
  initializeSystem();
  initializeSensors();
  initializePID();
  initializeWiFi();
  initDataLogging();
  
  // Load saved settings
  loadPIDSettings();
  
  // Initialize flight state
  flightState.currentMode = STABILIZE;
  flightState.displayMode = DISPLAY_BASIC;
  flightState.armed = false;
  flightState.calibrationMode = false;
  flightState.emergencyStop = false;
  flightState.flightStartTime = 0;
  flightState.totalFlightTime = 0;
  
  Serial.println("QuadFly Flight Controller v2.0 Ready!");
  blinkLED(3, 200);  // Ready signal
}

// ===== MAIN LOOP FUNCTION =====
void loop() {
  unsigned long currentTime = micros();
  float deltaTime = (currentTime - lastLoopTime) / 1000000.0;  // Convert to seconds
  lastLoopTime = currentTime;
  
  // Core flight loop - runs at maximum speed
  mainFlightLoop(deltaTime);
  
  // Lower frequency updates
  if (currentTime - displayUpdateTime > 100000) {  // 10Hz
    updateDisplay();
    displayUpdateTime = currentTime;
  }
  
  if (currentTime - telemetryUpdateTime > 200000) {  // 5Hz
    handleWebRequests();
    telemetryUpdateTime = currentTime;
  }
  
  if (currentTime - logUpdateTime > 100000) {  // 10Hz
    logFlightData();
    logUpdateTime = currentTime;
  }
  
  // Update performance metrics
  updatePerformanceMetrics();
}

// ===== SYSTEM INITIALIZATION =====
void initializeSystem() {
  // Initialize pins
  pinMode(LED_PIN, OUTPUT);
  pinMode(BATTERY_PIN, INPUT);

  // Initialize I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000);  // 400kHz I2C speed

  // Initialize display
  tft.initR(INITR_BLACKTAB);
  tft.setRotation(1);
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);
  tft.println("QuadFly v2.0");
  tft.println("Initializing...");

  // Initialize GPS serial
  gpsSerial.begin(9600, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);

  // Initialize RC input pins
  pinMode(RC_THROTTLE_PIN, INPUT);
  pinMode(RC_ROLL_PIN, INPUT);
  pinMode(RC_PITCH_PIN, INPUT);
  pinMode(RC_YAW_PIN, INPUT);
  pinMode(RC_AUX1_PIN, INPUT);
  pinMode(RC_AUX2_PIN, INPUT);
  pinMode(RC_AUX3_PIN, INPUT);
  pinMode(RC_AUX4_PIN, INPUT);

  // Initialize motors (ESCs)
  motorFL.attach(MOTOR_FL_PIN, MOTOR_MIN, MOTOR_MAX);
  motorFR.attach(MOTOR_FR_PIN, MOTOR_MIN, MOTOR_MAX);
  motorBL.attach(MOTOR_BL_PIN, MOTOR_MIN, MOTOR_MAX);
  motorBR.attach(MOTOR_BR_PIN, MOTOR_MIN, MOTOR_MAX);

  // Set motors to minimum (disarmed state)
  motorFL.writeMicroseconds(MOTOR_MIN);
  motorFR.writeMicroseconds(MOTOR_MIN);
  motorBL.writeMicroseconds(MOTOR_MIN);
  motorBR.writeMicroseconds(MOTOR_MIN);

  motors.motorsArmed = false;

  Serial.println("System initialization complete");
}

void initializeSensors() {
  tft.println("Init sensors...");

  // Initialize MPU6050
  mpu.begin();
  mpu.calcGyroOffsets(true);

  // Enable I2C bypass for HMC5883L
  Wire.beginTransmission(0x68);
  Wire.write(0x37);
  Wire.write(0x02);
  Wire.endTransmission();
  delay(100);

  mpu6050_available = true;
  Serial.println("MPU6050 initialized");

  // Initialize HMC5883L
  if (compass.begin()) {
    compass.setRange(HMC5883L_RANGE_1_3GA);
    compass.setMeasurementMode(HMC5883L_CONTINOUS);
    compass.setDataRate(HMC5883L_DATARATE_15HZ);
    compass.setSamples(HMC5883L_SAMPLES_8);
    hmc5883l_available = true;
    Serial.println("HMC5883L initialized");
  } else {
    Serial.println("HMC5883L initialization failed");
  }

  // Initialize MS5611
  if (barometer.begin()) {
    ms5611_available = true;
    Serial.println("MS5611 initialized");
  } else {
    Serial.println("MS5611 initialization failed");
  }

  // Check GPS
  gps_available = true;  // Will be verified during operation

  sensors.sensorsHealthy = checkSensorHealth();
  tft.println("Sensors ready");
}

void initializePID() {
  // Initialize Roll PID
  pidRoll.kp = PID_ROLL_KP;
  pidRoll.ki = PID_ROLL_KI;
  pidRoll.kd = PID_ROLL_KD;
  pidRoll.outputMin = -400;
  pidRoll.outputMax = 400;
  pidRoll.integralMax = 100;
  pidRoll.rateMode = false;

  // Initialize Pitch PID
  pidPitch.kp = PID_PITCH_KP;
  pidPitch.ki = PID_PITCH_KI;
  pidPitch.kd = PID_PITCH_KD;
  pidPitch.outputMin = -400;
  pidPitch.outputMax = 400;
  pidPitch.integralMax = 100;
  pidPitch.rateMode = false;

  // Initialize Yaw PID
  pidYaw.kp = PID_YAW_KP;
  pidYaw.ki = PID_YAW_KI;
  pidYaw.kd = PID_YAW_KD;
  pidYaw.outputMin = -400;
  pidYaw.outputMax = 400;
  pidYaw.integralMax = 100;
  pidYaw.rateMode = true;  // Yaw is rate mode by default

  // Initialize Altitude PID
  pidAlt.kp = PID_ALT_KP;
  pidAlt.ki = PID_ALT_KI;
  pidAlt.kd = PID_ALT_KD;
  pidAlt.outputMin = -500;
  pidAlt.outputMax = 500;
  pidAlt.integralMax = 200;
  pidAlt.rateMode = false;

  // Reset all PID states
  resetPIDIntegrals();

  Serial.println("PID controllers initialized");
}

void initializeWiFi() {
  tft.println("Starting WiFi...");

  WiFi.mode(WIFI_AP);
  WiFi.softAP(wifi_ssid, wifi_password);

  // Set up web server routes
  webServer.on("/", handleRoot);
  webServer.on("/telemetry", handleTelemetry);
  webServer.on("/pid", handlePIDSetup);
  webServer.on("/pid/update", HTTP_POST, handlePIDUpdate);
  webServer.on("/logs", handleLogs);
  webServer.onNotFound([]() {
    webServer.send(404, "text/plain", "Not found");
  });

  webServer.begin();

  Serial.print("WiFi AP started: ");
  Serial.println(wifi_ssid);
  Serial.print("IP address: ");
  Serial.println(WiFi.softAPIP());

  tft.println("WiFi ready");
}

// ===== CORE FLIGHT CONTROL =====
void mainFlightLoop(float deltaTime) {
  // Read all sensors
  readSensors();

  // Read RC inputs
  readRCInputs();

  // Check safety conditions
  checkSafetyConditions();

  // Process flight mode
  processFlightMode();

  // Calculate PID outputs (only if armed)
  if (flightState.armed && !flightState.emergencyStop) {
    calculatePIDOutputs(deltaTime);
  } else {
    // Reset PID integrals when disarmed
    resetPIDIntegrals();
  }

  // Update motor outputs
  updateMotors();

  // Detect vibration
  detectVibration();
}

void readSensors() {
  // Read MPU6050
  if (mpu6050_available) {
    mpu.update();
    sensors.roll = mpu.getAngleX();
    sensors.pitch = mpu.getAngleY();
    sensors.yaw = mpu.getAngleZ();
    sensors.gyroX = mpu.getGyroX();
    sensors.gyroY = mpu.getGyroY();
    sensors.gyroZ = mpu.getGyroZ();
    sensors.accelX = mpu.getAccX();
    sensors.accelY = mpu.getAccY();
    sensors.accelZ = mpu.getAccZ();
  }

  // Read HMC5883L
  if (hmc5883l_available) {
    Vector mag = compass.readNormalize();
    sensors.magX = mag.XAxis;
    sensors.magY = mag.YAxis;
    sensors.magZ = mag.ZAxis;

    // Calculate heading
    float heading = atan2(mag.YAxis, mag.XAxis);
    if (heading < 0) heading += 2 * PI;
    sensors.heading = heading * 180.0 / PI;
  }

  // Read MS5611
  if (ms5611_available) {
    sensors.pressure = barometer.readPressure();
    sensors.temperature = barometer.readTemperature();
    sensors.altitude = barometer.getAltitude(sensors.pressure);
  }

  // Read battery voltage
  int batteryRaw = analogRead(BATTERY_PIN);
  sensors.batteryVoltage = (batteryRaw / 4095.0) * 3.3 * 4.0;  // Voltage divider

  // Read GPS
  while (gpsSerial.available() > 0) {
    if (gps.encode(gpsSerial.read())) {
      gps_available = gps.location.isValid();
    }
  }

  sensors.sensorsHealthy = checkSensorHealth();
}

void readRCInputs() {
  // Read PWM signals
  rcInput.throttle = pulseIn(RC_THROTTLE_PIN, HIGH, 25000);
  rcInput.roll = pulseIn(RC_ROLL_PIN, HIGH, 25000);
  rcInput.pitch = pulseIn(RC_PITCH_PIN, HIGH, 25000);
  rcInput.yaw = pulseIn(RC_YAW_PIN, HIGH, 25000);
  rcInput.aux1 = pulseIn(RC_AUX1_PIN, HIGH, 25000);
  rcInput.aux2 = pulseIn(RC_AUX2_PIN, HIGH, 25000);
  rcInput.aux3 = pulseIn(RC_AUX3_PIN, HIGH, 25000);
  rcInput.aux4 = pulseIn(RC_AUX4_PIN, HIGH, 25000);

  // Check signal validity
  bool validSignal = (rcInput.throttle > 800 && rcInput.throttle < 2200) &&
                     (rcInput.roll > 800 && rcInput.roll < 2200) &&
                     (rcInput.pitch > 800 && rcInput.pitch < 2200) &&
                     (rcInput.yaw > 800 && rcInput.yaw < 2200);

  if (validSignal) {
    rcInput.signalLost = false;
    rcInput.signalQuality = 100;
    rcInput.lastUpdate = millis();
  } else {
    rcInput.signalLost = true;
    rcInput.signalQuality = 0;
  }

  // Signal timeout check
  if (millis() - rcInput.lastUpdate > 1000) {
    rcInput.signalLost = true;
    rcInput.signalQuality = 0;
  }
}

void processFlightMode() {
  // Flight mode switching based on AUX3 channel
  if (rcInput.aux3 < 1400) {
    flightState.currentMode = MANUAL;
  } else if (rcInput.aux3 < 1600) {
    flightState.currentMode = STABILIZE;
  } else {
    flightState.currentMode = ALTITUDE_HOLD;
  }

  // Arming logic - AUX1 switch
  static bool lastArmSwitch = false;
  bool currentArmSwitch = (rcInput.aux1 > 1700);

  if (currentArmSwitch && !lastArmSwitch) {  // Rising edge
    if (!flightState.armed && rcInput.throttle < 1100) {  // Throttle low
      flightState.armed = true;
      flightState.flightStartTime = millis();
      motors.motorsArmed = true;
      Serial.println("ARMED");
    } else if (flightState.armed) {
      flightState.armed = false;
      if (flightState.flightStartTime > 0) {
        flightState.totalFlightTime += millis() - flightState.flightStartTime;
      }
      motors.motorsArmed = false;
      Serial.println("DISARMED");
    }
  }
  lastArmSwitch = currentArmSwitch;
}

void calculatePIDOutputs(float deltaTime) {
  float rollSetpoint, pitchSetpoint, yawSetpoint, altSetpoint;

  // Convert RC inputs to setpoints based on flight mode
  switch (flightState.currentMode) {
    case MANUAL:
      // Direct motor control - no PID
      return;

    case STABILIZE:
      // Angle mode for roll/pitch, rate mode for yaw
      rollSetpoint = mapFloat(rcInput.roll, RC_MIN, RC_MAX, -30, 30);
      pitchSetpoint = mapFloat(rcInput.pitch, RC_MIN, RC_MAX, -30, 30);
      yawSetpoint = mapFloat(rcInput.yaw, RC_MIN, RC_MAX, -180, 180);
      break;

    case ALTITUDE_HOLD:
      // Same as stabilize but with altitude hold
      rollSetpoint = mapFloat(rcInput.roll, RC_MIN, RC_MAX, -30, 30);
      pitchSetpoint = mapFloat(rcInput.pitch, RC_MIN, RC_MAX, -30, 30);
      yawSetpoint = mapFloat(rcInput.yaw, RC_MIN, RC_MAX, -180, 180);
      altSetpoint = sensors.altitude;  // Hold current altitude
      break;

    case ACRO:
      // Rate mode for all axes
      rollSetpoint = mapFloat(rcInput.roll, RC_MIN, RC_MAX, -360, 360);
      pitchSetpoint = mapFloat(rcInput.pitch, RC_MIN, RC_MAX, -360, 360);
      yawSetpoint = mapFloat(rcInput.yaw, RC_MIN, RC_MAX, -360, 360);
      break;

    default:
      rollSetpoint = 0;
      pitchSetpoint = 0;
      yawSetpoint = 0;
      break;
  }

  // Calculate PID outputs
  float rollOutput = calculatePID(&pidRoll, rollSetpoint,
                                  pidRoll.rateMode ? sensors.gyroX : sensors.roll, deltaTime);
  float pitchOutput = calculatePID(&pidPitch, pitchSetpoint,
                                   pidPitch.rateMode ? sensors.gyroY : sensors.pitch, deltaTime);
  float yawOutput = calculatePID(&pidYaw, yawSetpoint,
                                 pidYaw.rateMode ? sensors.gyroZ : sensors.yaw, deltaTime);

  // Store outputs for motor mixing
  pidRoll.output = rollOutput;
  pidPitch.output = pitchOutput;
  pidYaw.output = yawOutput;

  // Altitude PID (if in altitude hold mode)
  if (flightState.currentMode == ALTITUDE_HOLD && ms5611_available) {
    pidAlt.output = calculatePID(&pidAlt, altSetpoint, sensors.altitude, deltaTime);
  } else {
    pidAlt.output = 0;
  }
}

void updateMotors() {
  if (!flightState.armed || flightState.emergencyStop || rcInput.signalLost) {
    // Disarmed or emergency - motors off
    motors.frontLeft = MOTOR_MIN;
    motors.frontRight = MOTOR_MIN;
    motors.backLeft = MOTOR_MIN;
    motors.backRight = MOTOR_MIN;
  } else if (flightState.currentMode == MANUAL) {
    // Manual mode - direct throttle control
    int baseThrottle = rcInput.throttle;
    motors.frontLeft = baseThrottle;
    motors.frontRight = baseThrottle;
    motors.backLeft = baseThrottle;
    motors.backRight = baseThrottle;
  } else {
    // Stabilized modes - mix PID outputs with throttle
    int baseThrottle = rcInput.throttle;

    // Add altitude hold adjustment
    if (flightState.currentMode == ALTITUDE_HOLD) {
      baseThrottle += (int)pidAlt.output;
    }

    // Motor mixing for quadcopter X configuration
    motors.frontLeft = baseThrottle - pidRoll.output + pidPitch.output - pidYaw.output;
    motors.frontRight = baseThrottle + pidRoll.output + pidPitch.output + pidYaw.output;
    motors.backLeft = baseThrottle - pidRoll.output - pidPitch.output + pidYaw.output;
    motors.backRight = baseThrottle + pidRoll.output - pidPitch.output - pidYaw.output;

    // Constrain motor outputs
    motors.frontLeft = constrain(motors.frontLeft, MOTOR_IDLE, MOTOR_MAX);
    motors.frontRight = constrain(motors.frontRight, MOTOR_IDLE, MOTOR_MAX);
    motors.backLeft = constrain(motors.backLeft, MOTOR_IDLE, MOTOR_MAX);
    motors.backRight = constrain(motors.backRight, MOTOR_IDLE, MOTOR_MAX);
  }

  // Write to ESCs
  motorFL.writeMicroseconds(motors.frontLeft);
  motorFR.writeMicroseconds(motors.frontRight);
  motorBL.writeMicroseconds(motors.backLeft);
  motorBR.writeMicroseconds(motors.backRight);
}

void checkSafetyConditions() {
  // Emergency stop conditions
  bool emergencyCondition = false;

  // Low battery
  if (sensors.batteryVoltage < 10.0 && sensors.batteryVoltage > 5.0) {
    emergencyCondition = true;
    Serial.println("EMERGENCY: Low battery!");
  }

  // Sensor failure
  if (!sensors.sensorsHealthy) {
    emergencyCondition = true;
    Serial.println("EMERGENCY: Sensor failure!");
  }

  // High vibration
  if (performance.vibrationLevel > 5.0) {
    emergencyCondition = true;
    Serial.println("EMERGENCY: High vibration!");
  }

  // RC signal lost while armed
  if (rcInput.signalLost && flightState.armed) {
    emergencyCondition = true;
    Serial.println("EMERGENCY: RC signal lost!");
  }

  if (emergencyCondition && !flightState.emergencyStop) {
    flightState.emergencyStop = true;
    flightState.currentMode = FAILSAFE;
  }

  // Emergency stop override - AUX2 switch
  if (rcInput.aux2 > 1700) {
    flightState.emergencyStop = true;
    flightState.armed = false;
  }
}

float calculatePID(PIDController* pid, float setpoint, float input, float deltaTime) {
  if (deltaTime <= 0) return 0;

  float error = setpoint - input;

  // Proportional term
  float pTerm = pid->kp * error;

  // Integral term with windup protection
  pid->integral += error * deltaTime;
  pid->integral = constrain(pid->integral, -pid->integralMax, pid->integralMax);
  float iTerm = pid->ki * pid->integral;

  // Derivative term (on measurement to avoid derivative kick)
  float derivative = -(input - pid->lastInput) / deltaTime;
  float dTerm = pid->kd * derivative;

  // Calculate output
  float output = pTerm + iTerm + dTerm;
  output = constrain(output, pid->outputMin, pid->outputMax);

  // Update for next iteration
  pid->lastInput = input;
  pid->setpoint = setpoint;
  pid->input = input;
  pid->output = output;

  // Performance tracking
  pid->errorSum += error * error;
  pid->sampleCount++;

  return output;
}

void resetPIDIntegrals() {
  pidRoll.integral = 0;
  pidPitch.integral = 0;
  pidYaw.integral = 0;
  pidAlt.integral = 0;
}

bool checkSensorHealth() {
  // Check if critical sensors are responding
  bool healthy = true;

  if (!mpu6050_available) {
    healthy = false;
  }

  // Check for reasonable sensor values
  if (abs(sensors.roll) > 90 || abs(sensors.pitch) > 90) {
    healthy = false;
  }

  if (sensors.batteryVoltage < 5.0 || sensors.batteryVoltage > 20.0) {
    healthy = false;
  }

  return healthy;
}

// ===== DISPLAY FUNCTIONS =====
void updateDisplay() {
  // Cycle display modes with AUX4 when disarmed
  static bool lastAux4 = false;
  bool currentAux4 = (rcInput.aux4 > 1700);

  if (currentAux4 && !lastAux4 && !flightState.armed) {
    flightState.displayMode = (DisplayMode)((flightState.displayMode + 1) % 5);
  }
  lastAux4 = currentAux4;

  // Draw appropriate display
  switch (flightState.displayMode) {
    case DISPLAY_BASIC:
      drawBasicDisplay();
      break;
    case DISPLAY_ARTIFICIAL_HORIZON:
      drawArtificialHorizon();
      break;
    case DISPLAY_GPS_MAP:
      drawGPSMap();
      break;
    case DISPLAY_PERFORMANCE:
      drawPerformanceDisplay();
      break;
    case DISPLAY_DIAGNOSTICS:
      drawDiagnosticsDisplay();
      break;
  }
}

void drawBasicDisplay() {
  tft.fillScreen(ST7735_BLACK);
  tft.setCursor(0, 0);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);

  // Flight status
  tft.print("Mode: ");
  switch (flightState.currentMode) {
    case MANUAL: tft.println("MANUAL"); break;
    case STABILIZE: tft.println("STAB"); break;
    case ALTITUDE_HOLD: tft.println("ALT HOLD"); break;
    case ACRO: tft.println("ACRO"); break;
    default: tft.println("OTHER"); break;
  }

  tft.print("Armed: ");
  tft.setTextColor(flightState.armed ? ST7735_RED : ST7735_GREEN);
  tft.println(flightState.armed ? "YES" : "NO");
  tft.setTextColor(ST7735_WHITE);

  // Attitude
  tft.print("Roll: "); tft.println(sensors.roll, 1);
  tft.print("Pitch: "); tft.println(sensors.pitch, 1);
  tft.print("Yaw: "); tft.println(sensors.yaw, 1);

  // Altitude and battery
  tft.print("Alt: "); tft.print(sensors.altitude, 1); tft.println("m");
  tft.print("Batt: "); tft.print(sensors.batteryVoltage, 1); tft.println("V");

  // RC signal
  tft.print("RC: ");
  if (rcInput.signalLost) {
    tft.setTextColor(ST7735_RED);
    tft.println("LOST");
  } else {
    tft.setTextColor(ST7735_GREEN);
    tft.print(rcInput.signalQuality); tft.println("%");
  }
  tft.setTextColor(ST7735_WHITE);

  // GPS
  tft.print("GPS: ");
  if (gps.location.isValid()) {
    tft.print(gps.satellites.value()); tft.println(" sats");
  } else {
    tft.println("NO FIX");
  }
}

void drawArtificialHorizon() {
  tft.fillScreen(ST7735_BLACK);

  int centerX = 64, centerY = 64, radius = 50;

  // Draw sky
  tft.fillCircle(centerX, centerY, radius, ST7735_BLUE);

  // Draw ground (simplified)
  int horizonY = centerY + (int)(sensors.pitch * 2);
  for (int y = horizonY; y < centerY + radius; y++) {
    if (y >= 0 && y < 128) {
      int width = sqrt(radius * radius - (y - centerY) * (y - centerY));
      tft.drawFastHLine(centerX - width, y, 2 * width, ST7735_GREEN);
    }
  }

  // Draw aircraft symbol
  tft.drawFastHLine(centerX - 15, centerY, 30, ST7735_YELLOW);
  tft.drawFastVLine(centerX, centerY - 5, 10, ST7735_YELLOW);

  // Display values
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(5, 5);
  tft.print("R:"); tft.println(sensors.roll, 1);
  tft.setCursor(5, 15);
  tft.print("P:"); tft.println(sensors.pitch, 1);
  tft.setCursor(5, 25);
  tft.print("H:"); tft.println(sensors.heading, 0);
}

void drawGPSMap() {
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);

  tft.println("GPS MAP");

  if (gps.location.isValid()) {
    tft.print("Lat: "); tft.println(gps.location.lat(), 6);
    tft.print("Lng: "); tft.println(gps.location.lng(), 6);
    tft.print("Alt: "); tft.print(gps.altitude.meters(), 1); tft.println("m");
    tft.print("Sats: "); tft.println(gps.satellites.value());
    tft.print("HDOP: "); tft.println(gps.hdop.value() / 100.0, 1);
  } else {
    tft.setTextColor(ST7735_RED);
    tft.println("NO GPS FIX");
  }
}

void drawPerformanceDisplay() {
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);

  tft.println("PERFORMANCE");
  tft.print("Loop: "); tft.print(performance.loopTime, 0); tft.println("us");
  tft.print("Roll RMS: "); tft.println(performance.rollRMS, 2);
  tft.print("Pitch RMS: "); tft.println(performance.pitchRMS, 2);
  tft.print("Yaw RMS: "); tft.println(performance.yawRMS, 2);

  tft.print("Vibration: ");
  if (performance.vibrationLevel > 2.0) {
    tft.setTextColor(ST7735_RED);
  } else if (performance.vibrationLevel > 1.0) {
    tft.setTextColor(ST7735_YELLOW);
  } else {
    tft.setTextColor(ST7735_GREEN);
  }
  tft.println(performance.vibrationLevel, 2);
  tft.setTextColor(ST7735_WHITE);

  tft.print("Flight: "); tft.print(flightState.totalFlightTime / 1000); tft.println("s");
}

void drawDiagnosticsDisplay() {
  tft.fillScreen(ST7735_BLACK);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);

  tft.println("DIAGNOSTICS");

  tft.print("MPU6050: ");
  tft.setTextColor(mpu6050_available ? ST7735_GREEN : ST7735_RED);
  tft.println(mpu6050_available ? "OK" : "FAIL");

  tft.setTextColor(ST7735_WHITE);
  tft.print("HMC5883L: ");
  tft.setTextColor(hmc5883l_available ? ST7735_GREEN : ST7735_RED);
  tft.println(hmc5883l_available ? "OK" : "FAIL");

  tft.setTextColor(ST7735_WHITE);
  tft.print("MS5611: ");
  tft.setTextColor(ms5611_available ? ST7735_GREEN : ST7735_RED);
  tft.println(ms5611_available ? "OK" : "FAIL");

  tft.setTextColor(ST7735_WHITE);
  tft.print("GPS: ");
  tft.setTextColor(gps_available ? ST7735_GREEN : ST7735_RED);
  tft.println(gps_available ? "OK" : "NO FIX");

  tft.setTextColor(ST7735_WHITE);
  tft.print("Health: ");
  tft.setTextColor(sensors.sensorsHealthy ? ST7735_GREEN : ST7735_RED);
  tft.println(sensors.sensorsHealthy ? "GOOD" : "BAD");
}

// ===== WEB INTERFACE =====
void handleWebRequests() {
  webServer.handleClient();
}

void handleRoot() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<title>QuadFly v2.0</title>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1'>";
  html += "<style>body{font-family:Arial;margin:20px;background:#f0f0f0;}";
  html += ".container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:10px;}";
  html += ".status{padding:10px;margin:10px 0;border-radius:5px;text-align:center;font-weight:bold;}";
  html += ".armed{background:#ffcccc;}.disarmed{background:#ccffcc;}";
  html += ".data{display:grid;grid-template-columns:1fr 1fr;gap:10px;}";
  html += ".card{background:#f9f9f9;padding:15px;border-radius:5px;}";
  html += "</style></head><body>";

  html += "<div class='container'>";
  html += "<h1>🚁 QuadFly Flight Controller v2.0</h1>";

  html += "<div class='status " + String(flightState.armed ? "armed" : "disarmed") + "'>";
  html += "Status: " + String(flightState.armed ? "ARMED" : "DISARMED");
  html += "</div>";

  html += "<div class='data'>";
  html += "<div class='card'><h3>Attitude</h3>";
  html += "<p>Roll: " + String(sensors.roll, 1) + "°</p>";
  html += "<p>Pitch: " + String(sensors.pitch, 1) + "°</p>";
  html += "<p>Yaw: " + String(sensors.yaw, 1) + "°</p></div>";

  html += "<div class='card'><h3>System</h3>";
  html += "<p>Battery: " + String(sensors.batteryVoltage, 1) + "V</p>";
  html += "<p>RC Quality: " + String(rcInput.signalQuality) + "%</p>";
  html += "<p>Flight Time: " + String(flightState.totalFlightTime / 1000) + "s</p></div>";
  html += "</div>";

  html += "<div style='margin-top:20px;text-align:center;'>";
  html += "<a href='/pid' style='margin:10px;padding:10px 20px;background:#007acc;color:white;text-decoration:none;border-radius:5px;'>🎛️ PID Tuning</a>";
  html += "<a href='/telemetry' style='margin:10px;padding:10px 20px;background:#28a745;color:white;text-decoration:none;border-radius:5px;'>📊 Telemetry</a>";
  html += "</div>";

  html += "</div>";
  html += "<script>setTimeout(function(){location.reload();}, 2000);</script>";
  html += "</body></html>";

  webServer.send(200, "text/html", html);
}

void handleTelemetry() {
  DynamicJsonDocument doc(1024);

  doc["timestamp"] = millis();
  doc["armed"] = flightState.armed;
  doc["mode"] = flightState.currentMode;
  doc["roll"] = sensors.roll;
  doc["pitch"] = sensors.pitch;
  doc["yaw"] = sensors.yaw;
  doc["altitude"] = sensors.altitude;
  doc["battery"] = sensors.batteryVoltage;
  doc["rcQuality"] = rcInput.signalQuality;
  doc["loopTime"] = performance.loopTime;
  doc["vibration"] = performance.vibrationLevel;
  doc["rollRMS"] = performance.rollRMS;
  doc["pitchRMS"] = performance.pitchRMS;
  doc["yawRMS"] = performance.yawRMS;

  if (gps.location.isValid()) {
    doc["gpsLat"] = gps.location.lat();
    doc["gpsLng"] = gps.location.lng();
    doc["gpsSats"] = gps.satellites.value();
  }

  String jsonString;
  serializeJson(doc, jsonString);
  webServer.send(200, "application/json", jsonString);
}

void handlePIDSetup() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<title>PID Tuning - QuadFly v2.0</title>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1'>";
  html += "<style>";
  html += "body{font-family:Arial;margin:20px;background:#f0f0f0;}";
  html += ".container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:10px;}";
  html += ".pid-section{margin:20px 0;padding:20px;background:#f9f9f9;border-radius:10px;}";
  html += ".pid-controls{display:grid;grid-template-columns:1fr 1fr 1fr;gap:15px;}";
  html += ".pid-input{display:flex;flex-direction:column;align-items:center;}";
  html += ".pid-input label{font-weight:bold;margin-bottom:5px;}";
  html += ".pid-input input{width:80px;padding:8px;border:2px solid #ddd;border-radius:5px;text-align:center;}";
  html += ".btn{padding:10px 20px;margin:5px;border:none;border-radius:5px;cursor:pointer;background:#007acc;color:white;}";
  html += ".status{padding:10px;margin:10px 0;border-radius:5px;text-align:center;font-weight:bold;}";
  html += ".armed{background:#ffcccc;}.disarmed{background:#ccffcc;}";
  html += "</style>";
  html += "<script>";
  html += "function updatePID(axis, gain, value) {";
  html += "  fetch('/pid/update', {";
  html += "    method: 'POST',";
  html += "    headers: {'Content-Type': 'application/x-www-form-urlencoded'},";
  html += "    body: 'axis=' + axis + '&gain=' + gain + '&value=' + value";
  html += "  }).then(response => response.text()).then(data => {";
  html += "    document.getElementById('status').innerHTML = data;";
  html += "    setTimeout(() => document.getElementById('status').innerHTML = '', 3000);";
  html += "  });";
  html += "}";
  html += "</script></head><body>";

  html += "<div class='container'>";
  html += "<h1>🎛️ PID Tuning Interface</h1>";

  html += "<div class='status " + String(flightState.armed ? "armed" : "disarmed") + "'>";
  html += "Flight Controller: " + String(flightState.armed ? "⚠️ ARMED" : "✅ DISARMED");
  html += "</div>";

  html += "<div id='status'></div>";

  // Roll PID
  html += "<div class='pid-section'>";
  html += "<h2>Roll Axis PID</h2>";
  html += "<div class='pid-controls'>";
  html += "<div class='pid-input'>";
  html += "<label>P</label>";
  html += "<input type='number' step='0.001' value='" + String(pidRoll.kp, 3) + "' ";
  html += "onchange='updatePID(\"roll\", \"p\", this.value)'>";
  html += "</div>";
  html += "<div class='pid-input'>";
  html += "<label>I</label>";
  html += "<input type='number' step='0.001' value='" + String(pidRoll.ki, 3) + "' ";
  html += "onchange='updatePID(\"roll\", \"i\", this.value)'>";
  html += "</div>";
  html += "<div class='pid-input'>";
  html += "<label>D</label>";
  html += "<input type='number' step='0.001' value='" + String(pidRoll.kd, 3) + "' ";
  html += "onchange='updatePID(\"roll\", \"d\", this.value)'>";
  html += "</div>";
  html += "</div></div>";

  // Similar sections for Pitch, Yaw, Altitude would go here...

  html += "<div style='text-align:center;'>";
  html += "<a href='/' class='btn'>🏠 Back to Dashboard</a>";
  html += "</div>";

  html += "</div></body></html>";

  webServer.send(200, "text/html", html);
}

void handlePIDUpdate() {
  if (!webServer.hasArg("axis") || !webServer.hasArg("gain") || !webServer.hasArg("value")) {
    webServer.send(400, "text/plain", "Missing parameters");
    return;
  }

  String axis = webServer.arg("axis");
  String gain = webServer.arg("gain");
  float value = webServer.arg("value").toFloat();

  if (value < 0 || value > 10) {
    webServer.send(400, "text/plain", "Value out of range");
    return;
  }

  PIDController* targetPID = nullptr;
  if (axis == "roll") targetPID = &pidRoll;
  else if (axis == "pitch") targetPID = &pidPitch;
  else if (axis == "yaw") targetPID = &pidYaw;
  else if (axis == "altitude") targetPID = &pidAlt;

  if (targetPID) {
    if (gain == "p") targetPID->kp = value;
    else if (gain == "i") targetPID->ki = value;
    else if (gain == "d") targetPID->kd = value;

    targetPID->integral = 0;  // Reset integral

    String response = "✅ " + axis + " " + gain + " updated to " + String(value, 3);
    webServer.send(200, "text/plain", response);
  } else {
    webServer.send(400, "text/plain", "Invalid axis");
  }
}

void handleLogs() {
  String html = "<!DOCTYPE html><html><head><title>Flight Logs</title></head><body>";
  html += "<h1>Flight Logs</h1>";
  html += "<p>Data logging system ready</p>";
  html += "<p><a href='/'>← Back to Dashboard</a></p>";
  html += "</body></html>";

  webServer.send(200, "text/html", html);
}

// ===== DATA LOGGING =====
void initDataLogging() {
  if (SPIFFS.begin(true)) {
    Serial.println("Data logging system initialized");
  } else {
    Serial.println("Data logging initialization failed");
  }
}

void logFlightData() {
  // Simple data logging - could be expanded
  static unsigned long lastLog = 0;
  if (millis() - lastLog > 1000) {  // Log every second
    if (flightState.armed) {
      Serial.print("LOG,");
      Serial.print(millis());
      Serial.print(",");
      Serial.print(sensors.roll);
      Serial.print(",");
      Serial.print(sensors.pitch);
      Serial.print(",");
      Serial.print(sensors.yaw);
      Serial.print(",");
      Serial.print(sensors.altitude);
      Serial.print(",");
      Serial.print(sensors.batteryVoltage);
      Serial.println();
    }
    lastLog = millis();
  }
}

void closeLogFile() {
  // Placeholder for log file closing
}

// ===== PID SETTINGS MANAGEMENT =====
void savePIDSettings() {
  preferences.begin("pid_settings", false);
  preferences.putFloat("roll_kp", pidRoll.kp);
  preferences.putFloat("roll_ki", pidRoll.ki);
  preferences.putFloat("roll_kd", pidRoll.kd);
  preferences.putFloat("pitch_kp", pidPitch.kp);
  preferences.putFloat("pitch_ki", pidPitch.ki);
  preferences.putFloat("pitch_kd", pidPitch.kd);
  preferences.putFloat("yaw_kp", pidYaw.kp);
  preferences.putFloat("yaw_ki", pidYaw.ki);
  preferences.putFloat("yaw_kd", pidYaw.kd);
  preferences.putFloat("alt_kp", pidAlt.kp);
  preferences.putFloat("alt_ki", pidAlt.ki);
  preferences.putFloat("alt_kd", pidAlt.kd);
  preferences.end();
  Serial.println("PID settings saved");
}

void loadPIDSettings() {
  preferences.begin("pid_settings", true);
  pidRoll.kp = preferences.getFloat("roll_kp", PID_ROLL_KP);
  pidRoll.ki = preferences.getFloat("roll_ki", PID_ROLL_KI);
  pidRoll.kd = preferences.getFloat("roll_kd", PID_ROLL_KD);
  pidPitch.kp = preferences.getFloat("pitch_kp", PID_PITCH_KP);
  pidPitch.ki = preferences.getFloat("pitch_ki", PID_PITCH_KI);
  pidPitch.kd = preferences.getFloat("pitch_kd", PID_PITCH_KD);
  pidYaw.kp = preferences.getFloat("yaw_kp", PID_YAW_KP);
  pidYaw.ki = preferences.getFloat("yaw_ki", PID_YAW_KI);
  pidYaw.kd = preferences.getFloat("yaw_kd", PID_YAW_KD);
  pidAlt.kp = preferences.getFloat("alt_kp", PID_ALT_KP);
  pidAlt.ki = preferences.getFloat("alt_ki", PID_ALT_KI);
  pidAlt.kd = preferences.getFloat("alt_kd", PID_ALT_KD);
  preferences.end();
  Serial.println("PID settings loaded");
}

// ===== UTILITY FUNCTIONS =====
void updatePerformanceMetrics() {
  static unsigned long lastUpdate = 0;
  static unsigned long loopCount = 0;
  static unsigned long totalLoopTime = 0;

  unsigned long currentTime = micros();
  if (lastUpdate > 0) {
    unsigned long loopTime = currentTime - lastUpdate;
    totalLoopTime += loopTime;
    loopCount++;

    if (loopCount >= 100) {
      performance.loopTime = totalLoopTime / loopCount;
      totalLoopTime = 0;
      loopCount = 0;
    }

    if (loopTime > 2500) {
      performance.loopTimeViolations++;
    }
  }
  lastUpdate = currentTime;

  // Calculate RMS errors
  static int sampleCount = 0;
  sampleCount++;
  if (sampleCount >= 1000) {
    if (pidRoll.sampleCount > 0) {
      performance.rollRMS = sqrt(pidRoll.errorSum / pidRoll.sampleCount);
      pidRoll.errorSum = 0;
      pidRoll.sampleCount = 0;
    }
    if (pidPitch.sampleCount > 0) {
      performance.pitchRMS = sqrt(pidPitch.errorSum / pidPitch.sampleCount);
      pidPitch.errorSum = 0;
      pidPitch.sampleCount = 0;
    }
    if (pidYaw.sampleCount > 0) {
      performance.yawRMS = sqrt(pidYaw.errorSum / pidYaw.sampleCount);
      pidYaw.errorSum = 0;
      pidYaw.sampleCount = 0;
    }
    sampleCount = 0;
  }
}

void detectVibration() {
  static float accelHistory[10];
  static int historyIndex = 0;
  static bool historyFull = false;

  float totalAccel = sqrt(sensors.accelX * sensors.accelX +
                         sensors.accelY * sensors.accelY +
                         sensors.accelZ * sensors.accelZ);

  accelHistory[historyIndex] = totalAccel;
  historyIndex = (historyIndex + 1) % 10;
  if (historyIndex == 0) historyFull = true;

  if (historyFull) {
    float mean = 0;
    for (int i = 0; i < 10; i++) {
      mean += accelHistory[i];
    }
    mean /= 10.0;

    float variance = 0;
    for (int i = 0; i < 10; i++) {
      float diff = accelHistory[i] - mean;
      variance += diff * diff;
    }
    variance /= 10.0;

    performance.vibrationLevel = sqrt(variance);
  }
}

float mapFloat(float x, float in_min, float in_max, float out_min, float out_max) {
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

void blinkLED(int times, int delayMs) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(LED_PIN, LOW);
    delay(delayMs);
  }
}

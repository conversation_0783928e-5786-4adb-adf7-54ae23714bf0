# QuadFly Wiring Checklist

Use this checklist to verify all connections before powering on your quadcopter.

## Power Connections ✓
- [ ] ESP32 VIN connected to battery positive (through voltage regulator if needed)
- [ ] ESP32 GND connected to battery negative
- [ ] All sensor modules have 3.3V power
- [ ] All sensor modules have ground connections
- [ ] Battery voltage divider connected to pin 35

## I2C Bus (GY-86 Module) ✓
- [ ] SDA (pin 21) → GY-86 SDA
- [ ] SCL (pin 22) → GY-86 SCL
- [ ] 3.3V → GY-86 VCC
- [ ] GND → GY-86 GND
- [ ] Pull-up resistors present (usually on module)

## GPS Module ✓
- [ ] Pin 16 (RX) → GPS TX
- [ ] Pin 17 (TX) → GPS RX
- [ ] 3.3V → GPS VCC
- [ ] GND → GPS GND

## RC Receiver ✓
- [ ] Pin 32 → Channel 1 (Throttle)
- [ ] Pin 33 → Channel 2 (Roll)
- [ ] Pin 25 → Channel 3 (Pitch)
- [ ] Pin 26 → Channel 4 (Yaw)
- [ ] Pin 27 → Channel 5 (Flight Mode)
- [ ] Pin 14 → Channel 6 (Aux 1)
- [ ] Pin 12 → Channel 7 (Aux 2)
- [ ] Pin 13 → Channel 8 (Aux 3)
- [ ] RC receiver powered (5V or 3.3V depending on model)

## Motor ESCs ✓
- [ ] Pin 2 → ESC 1 Signal (Front Right Motor)
- [ ] Pin 4 → ESC 2 Signal (Front Left Motor)
- [ ] Pin 5 → ESC 3 Signal (Rear Left Motor)
- [ ] Pin 18 → ESC 4 Signal (Rear Right Motor)
- [ ] All ESCs connected to battery power
- [ ] All ESCs have common ground with ESP32

## TFT Display (ST7735) ✓
- [ ] Pin 15 → CS (Chip Select)
- [ ] Pin 19 → RST (Reset)
- [ ] Pin 23 → DC/A0 (Data/Command)
- [ ] Pin 23 → MOSI (Data)
- [ ] Pin 18 → SCLK (Clock)
- [ ] 3.3V → VCC
- [ ] GND → GND

## Motor Rotation Check ✓
Verify motor rotation directions (remove propellers first!):
- [ ] Motor 1 (Front Right): Clockwise (CW)
- [ ] Motor 2 (Front Left): Counter-clockwise (CCW)
- [ ] Motor 3 (Rear Left): Clockwise (CW)
- [ ] Motor 4 (Rear Right): Counter-clockwise (CCW)

## Pre-Flight Safety Check ✓
- [ ] All connections secure and insulated
- [ ] No short circuits
- [ ] Battery voltage correct (11.1V for 3S LiPo)
- [ ] Propellers removed for initial testing
- [ ] RC transmitter bound to receiver
- [ ] Clear area for testing
- [ ] Emergency stop procedure ready

## Software Verification ✓
- [ ] Code uploaded successfully
- [ ] Serial monitor shows sensor initialization
- [ ] TFT display shows flight data
- [ ] RC inputs responding in serial monitor
- [ ] GPS acquiring satellites (if outdoors)
- [ ] All sensors providing data

## Notes Section
Write any specific notes about your build:

```
Date: ___________
Builder: ___________

Specific modifications:
- 
- 
- 

Issues encountered:
- 
- 
- 

Test results:
- 
- 
- 
```

## Emergency Procedures

### If Something Goes Wrong:
1. **Immediately disconnect battery**
2. Check all connections
3. Review serial monitor for error messages
4. Verify component functionality individually
5. Re-read documentation

### Before First Flight:
1. Complete all checklist items
2. Test with propellers removed
3. Verify all controls respond correctly
4. Check fail-safe operation
5. Start with very short hover tests

**Remember: Safety first! Never skip safety checks.**
